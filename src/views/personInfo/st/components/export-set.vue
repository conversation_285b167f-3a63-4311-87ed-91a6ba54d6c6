<!-- 编辑弹窗 -->
<template>
  <ele-drawer
    :size="730"
    title="自定义导出设置"
    :append-to-body="true"
    :destroy-on-close="true"
    style="max-width: 100%"
    :model-value="modelValue"
    :body-style="{ paddingBottom: '8px' }"
    @update:modelValue="updateModelValue"
  >
    <ele-page>
      <el-form
        ref="formRef"
        size="small"
        :model="form"
        :rules="rules"
        label-position="top"
        label-width="100px"
        @submit.prevent=""
      >
        <el-form-item label="导出文件名" prop="fileName">
          <el-input
            clearable
            v-model="form.fileName"
            placeholder="请输入导出文件名"
          />
        </el-form-item>
        <el-form-item label="导出数据" prop="type">
          <el-radio-group v-model="form.type">
            <el-radio-button
              v-for="type in exportType"
              :key="type.label"
              :value="type.label"
            >
              {{ type.name }}
            </el-radio-button>
          </el-radio-group>
        </el-form-item>
        <ele-alert
          :title="
            '请选择需要导出的字段，勾选顺序即为表头字段顺序，当前已勾选 ' +
            leftOperationArray.length +
            ' 个。'
          "
          show-icon
          style="margin-bottom: 12px"
          :closable="false"
        />
        <template v-for="item in initItems">
          <el-form-item>
            <el-checkbox
              v-model="checkAll[item.type]"
              :indeterminate="isIndeterminate[item.type]"
              @change="handleCheckAll(item.type, $event)"
            >
              {{ item.typeName }}
            </el-checkbox>
          </el-form-item>
          <el-form-item>
            <el-checkbox-group v-model="leftOperation[item.type]">
              <el-checkbox
                style="min-width: 80px"
                v-for="field in item.list"
                :key="field.fieldEn"
                :value="field.fieldEn"
                @change="handleCheckedChange(field, item.type)"
              >
                {{ field.fieldZh }}
              </el-checkbox>
            </el-checkbox-group>
          </el-form-item>
          <el-divider style="margin: 0 0 10px 0; opacity: 0.6" />
        </template>
      </el-form>
    </ele-page>
    <template #footer>
      <el-button @click="updateModelValue(false)">取消</el-button>
      <el-button type="primary" :loading="loading" @click="save">
        保存
      </el-button>
    </template>
  </ele-drawer>
</template>

<script setup>
  import { reactive, ref, unref, watch } from 'vue';
  import { EleMessage } from 'ele-admin-plus/es';
  import { useFormData } from '@/utils/use-form-data';
  import { getFieldList } from '../api/index';
  import { useRouter } from 'vue-router';
  import { compare, groupArr, mergeUnique } from '@/utils/common';
  import { useUserStore } from '@/store/modules/user';
  import { storeToRefs } from 'pinia';
  import { useDictData } from '@/utils/use-dict-data';

  const emit = defineEmits(['done', 'update:modelValue']);

  const props = defineProps({
    /** 弹窗是否打开 */
    modelValue: Boolean,
    /** 修改回显的数据 */
    data: Object
  });
  const { currentRoute, push } = useRouter();
  const { params, path } = unref(currentRoute);
  const routeType = path.split('/')[3];

  const userStore = useUserStore();
  const { dicts } = storeToRefs(userStore);
  let dicCodes = ['groupType'];
  dicCodes.forEach((code) => {
    if (dicts.value[code]) {
      return;
    } else {
      useDictData(code, { userType: routeType });
    }
  });
  /** 表单项 */
  const initItems = ref([]);
  /** 获取表单字段 */
  const initModel = ref([]);

  const exportType = [
    { label: '1', name: '导出当前页数据' },
    { label: '2', name: '仅导出选中的数据' },
    { label: '3', name: '仅导出查询结果' },
    { label: '4', name: '导出全部数据' }
  ];

  /** 提交状态 */
  const loading = ref(false);

  /** 表单实例 */
  const formRef = ref(null);

  /** 表单数据 */
  const [form, resetFields, assignFields] = useFormData({
    fileName: '',
    type: ''
  });

  /** 表单验证规则 */
  const rules = reactive({
    fileName: [
      {
        required: true,
        message: '请输入导出文件名',
        type: 'string',
        trigger: 'blur'
      }
    ],
    type: [
      {
        required: true,
        message: '请选择导出数据类型',
        type: 'string',
        trigger: 'blur'
      }
    ]
  });

  /** 保存编辑 */
  const save = () => {
    formRef.value?.validate?.((valid) => {
      if (!valid) {
        return;
      }
      if (leftOperationArray.value.length === 0) {
        EleMessage.error('请选择需要导出的字段');
        return;
      }
      let newData = {
        ...form,
        fields: leftOperationArray.value.map((item) => item.fieldEn).join(',')
      };
      updateModelValue(false);
      emit('done', newData);
    });
  };

  /** 更新modelValue */
  const updateModelValue = (value) => {
    emit('update:modelValue', value);
  };

  watch(
    () => props.modelValue,
    (modelValue) => {
      if (modelValue) {
        resetFields();
        formRef.value?.clearValidate?.();
      }
    }
  );

  const isIndeterminate = ref({});
  const checkAll = ref({});
  const checkedGroup = ref(null);
  // 左侧数据
  const leftOperation = ref({});
  // 右侧数据
  const leftOperationArray = ref([]);

  /** 全选反选的多选框 */
  const handleCheckAll = (type, val) => {
    checkedGroup.value = type;
    isIndeterminate.value[type] = false;
    let allDataCode = [];
    let allData = [];
    let leftData = initItems.value.filter((obj) => obj.type === type);
    if (leftData.length > 0) {
      leftData[0].list.forEach((e) => {
        allDataCode.push(e.fieldEn);
        allData.push(e);
      });
    }
    if (val) {
      leftOperation.value[type] = allDataCode;
    } else {
      leftOperation.value[type] = [];
    }
    leftOperationArray.value = mergeUnique(
      leftOperationArray.value,
      allData,
      'fieldEn'
    );
  };

  const handleCheckedChange = (e, type) => {
    checkedGroup.value = type;
    let checkedCount = leftOperation.value[type]
      ? leftOperation.value[type].length
      : 0;
    let existTypeData = initItems.value.filter((obj) => obj.type === type);
    let dataCount = existTypeData.length > 0 ? existTypeData[0].listLength : 0;
    checkAll.value[type] = checkedCount === dataCount;
    isIndeterminate.value[type] = checkedCount > 0 && checkedCount < dataCount;

    let isExist = leftOperationArray.value.filter(
      (obj) => obj.fieldEn === e.fieldEn
    );
    if (isExist.length > 0) {
      leftOperationArray.value.forEach((v, i) => {
        if (v.fieldEn === e.fieldEn) {
          leftOperationArray.value.splice(
            leftOperationArray.value.indexOf(v),
            1
          );
        }
      });
    } else {
      leftOperationArray.value.push({
        ...e
      });
    }
  };

  const initFieldList = async () => {
    const fieldData = await getFieldList(routeType, {
      exportFieldFlag: '是',
      listFlag: '否'
    });
    if (fieldData.length > 0) {
      let rData = groupArr(fieldData, 'groupId');
      rData.forEach((data) => {
        let dictData = dicts.value['groupType'].filter(
          (gt) => gt.dictDataCode === data.type
        );
        if (dictData.length > 0) {
          data.sort = dictData[0].sort;
          data.typeName = dictData[0].dictDataName;
        }
      });
      initItems.value = rData.sort(compare('sort'));
    }
  };

  initFieldList();
</script>
