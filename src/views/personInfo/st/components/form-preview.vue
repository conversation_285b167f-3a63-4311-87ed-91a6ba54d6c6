<template>
  <ele-card
    :body-style="{ height: 'auto', padding: '10px 5px 0 5px !important' }"
    :header="currentGroup.title"
  >
    initModel=={{ initModel }}<br />
    <!--    currentData=={{ currentData }}<br/>-->
    <el-form
      ref="proFormRef"
      :model="initModel"
      label-width="auto"
      size="small"
      @submit.prevent=""
    >
      <el-row :gutter="6">
        <!--        <template v-if="currentGroup.infoType">-->
        <!--          <el-col v-for="item in initItems" :lg="8" :md="12" :sm="12" :xs="24">-->
        <!--            -->
        <!--          </el-col>-->
        <!--        </template>-->
        <!--        <template v-else>-->
        <template v-for="item in initItems">
          <el-col
            :lg="8"
            :md="12"
            :sm="12"
            :xs="24"
            v-if="item.showFlag === '是'"
          >
            <ProFormItem
              :item="item"
              :model="initModel"
              :rules="rules"
              :diyFormItem="true"
              @updateItemValue="updateFormValue"
              @change="(value) => onchange(item, value)"
            >
              <template
                v-for="name in Object.keys($slots).filter(
                  (k) =>
                    !['default', 'footer', 'topExtra', 'bottomExtra'].includes(
                      k
                    )
                )"
                #[name]="slotProps"
              >
                <slot :name="name" v-bind="slotProps || {}"></slot>
              </template>
            </ProFormItem>
          </el-col>
          <el-col
            :lg="8"
            :md="12"
            :sm="12"
            :xs="24"
            v-else-if="item.showFlag === 'readonly'"
          >
            <el-form-item :label="item.label" :required="item.required">
              <el-input
                :model-value="initModel[item.prop]"
                readonly
                :placeholder="
                  (item.type === 'input' ? '请输入' : '请选择') + item.label
                "
              />
            </el-form-item>
          </el-col>
        </template>
      </el-row>
    </el-form>
  </ele-card>
</template>

<script setup>
  import { nextTick, reactive, ref, watch } from 'vue';
  import { useFormData } from '@/utils/use-form-data';
  import ProFormItem from '@/components/ProForm/components/pro-form-item.vue';
  import {
    arrayTypes,
    regionsArrayTypes,
    selectTypes,
    stringTypes,
    uploadTypes
  } from '@/components/ProForm/util';
  import { getFieldList, getPersonInfoData } from '../api/index';
  import { EleMessage } from 'ele-admin-plus';
  import { isImageFile } from '@/utils/common';
  import { getFormTemplateField } from '@/views/zizhu/api/form-template-field-index.js';
  import { getFormApplyFieldList } from '@/views/zizhu/apply/api/index.js';

  const BASE_URL = import.meta.env.BASE_URL;
  const emit = defineEmits(['onDoneGroup', 'onDoneDeleteFile', 'valid']);

  const props = defineProps({
    /** 表单标题宽度 */
    labelWidth: Number,
    /** 表单显示列数 */
    grid: Number,
    currentGroup: Object,
    routeType: String,
    userType: String,
    currentData: Object
  });

  const currentData = ref({});
  console.log(props.grid);
  console.log(props.currentData);
  currentData.value = props?.currentData ?? {};
  /** 字段 */
  const fieldData = ref([]);
  /** 请求状态 */
  const loading = ref(true);

  const proFormRef = ref();

  /** 表单项 */
  const initItems = ref([]);
  /** 获取表单字段 */
  const initModel = ref([]);
  const rules = reactive({});
  /** 表单数据 */
  const [form, resetFields, setFieldValue] = useFormData(initModel);

  const queryDictionaryField = (group) => {
    loading.value = true;
    getFieldList(props.routeType, { groupId: group.id })
      .then((list) => {
        loading.value = false;
        fieldData.value = list;
        if (list) {
          formatFormData(list);
        }
      })
      .catch((e) => {
        EleMessage.error(e.message);
      });
  };

  // 子页面的校验方法，status用来判断是否校验成功
  const validateHandle = () => {
    console.log('调用子组件中的validateHandle校验方法');
    let status = null;
    proFormRef.value?.validate?.((valid, obj) => {
      if (!valid) {
        status = false;
      } else {
        status = true;
      }
      // emit('valid', status);
      return status;
    });
  };

  /** 更新表单删除图片/附件ID */
  const onchange = (item, value) => {
    emit('onDoneDeleteFile', {
      prop: item.prop,
      values: value
    });
  };

  // watch(
  //     () => fieldData.value, (fieldData) => {
  //       let fieldResult = []
  //       if (fieldData && fieldData.length > 0) {
  //         fieldData.forEach((e, index) => {
  //           let props = {}
  //           let selfFieldLink = ''
  //           let nextField = ''
  //           let type = e.controlType
  //           if (e.controlType === "select") {
  //             if (e.loadDataType && e.loadDataType !== 'other') {
  //               type = 'dictSelect'
  //               props = {
  //                 code: e.loadDataType,
  //                 dicQueryParams: {
  //                   getValType: e.loadDataType !== 'pycc' ? 'name' : ''
  //                 },
  //                 filterable: true
  //               }
  //             } else {//院系专业班级页面逻辑处理
  //               type = 'dictSelect'
  //               if (e.loadDataUrl === '/code/codeDwb') {
  //                 selfFieldLink = 'dwmc'
  //                 nextField = 'zymc'
  //               }
  //               if (e.loadDataUrl === '/code/codeZyb') {
  //                 selfFieldLink = 'zymc'
  //                 nextField = 'bjmc'
  //               }
  //               if (e.loadDataUrl === '/code/codeBjb') {
  //                 selfFieldLink = 'bjmc'
  //               }
  //               props = {
  //                 code: e.loadDataType,
  //                 filterable: true,
  //                 dicQueryParams: {
  //                   dictFieldUrl: e.loadDataUrl,
  //                   valueField: e.valueField,
  //                   textField: e.textField,
  //                 },
  //               }
  //             }
  //           } else if (e.controlType === "fileUpload" || e.controlType === "imageUpload") {
  //             //附件上传数量
  //             props = {
  //               limit: e.regExpression ? Number(e.regExpression) : 1,
  //             }
  //           }
  //           // ...e,
  //           let newObj = Object.assign({
  //             key: 'fieldKey_' + index,
  //             label: e.fieldZh,
  //             prop: e.fieldEn,
  //             type: type,
  //             required: e.required === '是' ? true : false,
  //             showFlag: e.showFlag,
  //             fieldLinks: e.fieldLinks,
  //             selfFieldLink: selfFieldLink,
  //             nextField: nextField,
  //           }, {
  //             props: props,
  //           });
  //           fieldResult.push(newObj)
  //
  //           if (e.controlType === "regions") {
  //             //省市区+详细地址信息
  //             fieldResult.push({
  //               key: 'fieldKey_99' + index,
  //               label: '详细地址',
  //               prop: e.fieldEn + '_regionsDetail',
  //               type: 'input',
  //               required: false,
  //               showFlag: '否',
  //             })
  //
  //           }
  //         })
  //         initItems.value = fieldResult
  //       }
  //       let fieldFormResult = {}
  //       if (fieldResult && fieldResult.length > 0) {
  //         fieldResult.forEach(item => {
  //           if (item.type) {
  //             if (uploadTypes.includes(item.type) || regionsArrayTypes.includes(item.type) || arrayTypes.includes(item.type)) {
  //               fieldFormResult[item.prop] = [];
  //             }
  //             if (stringTypes.includes(item.type) || selectTypes.includes(item.type)) {
  //               fieldFormResult[item.prop] = '';
  //             }
  //           }
  //         });
  //         initModel.value = fieldFormResult;
  //       }
  //
  //       nextTick(() => {
  //         console.log("fieldResult====",fieldResult)
  //         console.log("props.data====",props.data)
  //         if (props.data) {
  //           if (fieldResult && fieldResult.length > 0) {
  //             fieldResult.forEach(item => {
  //               console.log("item===", item, item.prop, props.data.value)
  //               let setValue = null;
  //               let dataValue = props.data.value ? props.data.value[item.prop] : null;
  //               if (dataValue) {
  //                 if (regionsArrayTypes.includes(item.type)) {
  //                   let regionsArr = dataValue.split(',')
  //                   if (item.type === 'regions') {
  //                     if (regionsArr.length > 0 && regionsArr[3]) {
  //                       initItems.value.filter(f => {
  //                         if (f.prop === item.prop + '_regionsDetail') {
  //                           initModel.value[f.prop] = regionsArr[3];
  //                         }
  //                       })
  //                     }
  //                     regionsArr.splice(regionsArr.length - 1, 1)
  //                     setValue = regionsArr
  //                   } else {
  //                     setValue = regionsArr
  //                   }
  //                 } else if (item.type === 'imageUpload') {
  //                   const oldFiles = dataValue
  //                       ? JSON.parse(dataValue).map((d, i) => {
  //                         return {
  //                           key: d.id,
  //                           name: d.originalFilename,
  //                           contentType: d.contentType,
  //                           url: BASE_URL + 'api/file/inline/' + d.id,
  //                           status: 'done'
  //                         };
  //                       }) : [];
  //                   setValue = oldFiles;
  //                 } else if (item.type === 'fileUpload') {
  //                   const oldFiles = dataValue
  //                       ? JSON.parse(dataValue).map((d, i) => {
  //                         let newObj = {
  //                           key: d.id,
  //                           name: d.originalFilename,
  //                           contentType: d.contentType,
  //                           fileUrl: BASE_URL + 'api/file/inline/' + d.id,
  //                           status: 'done'
  //                         }
  //                         let mark = isImageFile(newObj)
  //                         newObj.isImageFile = mark;
  //                         if (mark) newObj.url = newObj.fileUrl
  //                         return newObj;
  //                       }) : [];
  //                   setValue = oldFiles;
  //                 } else {
  //                   setValue = dataValue
  //                 }
  //               }
  //               if (setValue) updateFormValue(item, setValue)
  //             });
  //           }
  //         }
  //       })
  //     },
  //     { immediate: true }
  // )

  /** 更新表单数据 */
  const updateFormValue = (item, value) => {
    initModel.value[item.prop] = value;
    if (item.type === 'regions' && value) {
      initItems.value.filter((f) => {
        if (f.prop === item.prop + '_regionsDetail') f.showFlag = '是';
      });
    }
    if (item.selfFieldLink) {
      //院系专业班级三个字段页面逻辑特殊处理
      nextTick(() => {
        initItems.value.filter((f) => {
          if (f.selfFieldLink === item.nextField) {
            f.showFlag = '是';
            f.props['refresh'] = value; //watch每次刷新，重新请求
            if (item.nextField === 'zymc') {
              f.props.dicQueryParams['params'] = { dwid: value };
            }
            if (item.nextField === 'bjmc') {
              f.props.dicQueryParams['params'] = { zyid: value };
            }
          }
        });
      });
    }
    if (item.fieldLinks) {
      //获取选择值下面所有的显示字段信息
      let showfields = item.fieldLinks.filter((f) => {
        // return f.fieldValId === value
        return f.fieldVal === value;
      });
      if (showfields.length > 0) {
        showfields.forEach((sf) => {
          nextTick(() => {
            //切换选择值，清空之前选项所赋值
            initModel.value[sf.linkField] = '';
            //动态切换选择值，关联具体字段的显示隐藏
            initItems.value.filter((init) => {
              init.showFlag =
                sf.linkField === init.prop ? sf.showFlag : init.showFlag;
              if (sf.linkFieldDataType && sf.linkField === init.prop) {
                init.props = {
                  code: sf.linkFieldDataType,
                  filterable: true
                };
              }
            });
          });
        });
      }
    }

    if (props.currentGroup && props.currentGroup.listFlag !== '是') {
      emit('onDoneGroup', {
        groupId: props.currentGroup.id,
        groupName: props.currentGroup.title,
        values: initModel.value
      });
    }
  };

  /** 查询组的申请字段信息 */
  const queryFormTemplateField = () => {
    loading.value = true;
    getFormTemplateField({
      projectId: props.currentGroup.projectId,
      groupId: props.currentGroup.id,
      type: props.routeType
    })
      .then((list) => {
        loading.value = false;
        if (list) {
          list.forEach((item) => {
            item.showFlag = 'readonly'; //预览只读
          });
          formatFormData(list);
        }
        fieldData.value = list ?? [];
        console.log('222===', list);
      })
      .catch((e) => {
        loading.value = false;
        EleMessage.error(e.message);
      });
  };

  /**资助项目申请- 查询组的申请字段信息 */
  const queryFormApplyFieldList = () => {
    loading.value = true;
    getFormApplyFieldList(props.currentGroup.projectId, {
      projectId: props.currentGroup.projectId,
      groupId: props.currentGroup.id,
      type: props.routeType
    })
      .then((list) => {
        loading.value = false;
        if (list) {
          list.forEach((item) => {
            if (item.infoType === 'base') {
              item.showFlag = 'readonly';
              // } else {
              //   item.showFlag = '是'//可编辑
            }
          });
          console.log(currentData.value);
          formatFormData(list);
        }
        fieldData.value = list ?? [];
        console.log('1111===', list);
      })
      .catch((e) => {
        loading.value = false;
        EleMessage.error(e.message);
      })
      .finally(() => {});
  };

  /** 查询用户基本信息 */
  const queryPersonInfo = async () => {
    loading.value = true;
    await getPersonInfoData()
      .then((data) => {
        loading.value = false;
        currentData.value = data;
        console.log(currentData.value);
      })
      .catch((e) => {
        EleMessage.error(e.message);
      });
  };

  const formatFormData = (fieldData) => {
    setTimeout(() => {
      let fieldResult = [];
      if (fieldData && fieldData.length > 0) {
        fieldData.forEach((e, index) => {
          let props = {};
          let selfFieldLink = '';
          let nextField = '';
          let type = e.controlType;
          if (e.controlType === 'select') {
            if (e.loadDataType && e.loadDataType !== 'other') {
              type = 'dictSelect';
              props = {
                code: e.loadDataType,
                dicQueryParams: {
                  getValType: e.loadDataType !== 'pycc' ? 'name' : ''
                },
                filterable: true
              };
            } else {
              //院系专业班级页面逻辑处理
              type = 'dictSelect';
              if (e.loadDataUrl === '/code/codeDwb') {
                selfFieldLink = 'dwmc';
                nextField = 'zymc';
              }
              if (e.loadDataUrl === '/code/codeZyb') {
                selfFieldLink = 'zymc';
                nextField = 'bjmc';
              }
              if (e.loadDataUrl === '/code/codeBjb') {
                selfFieldLink = 'bjmc';
              }
              props = {
                code: e.loadDataType,
                filterable: true,
                dicQueryParams: {
                  dictFieldUrl: e.loadDataUrl,
                  valueField: e.valueField,
                  textField: e.textField
                }
              };
            }
          } else if (
            e.controlType === 'fileUpload' ||
            e.controlType === 'imageUpload'
          ) {
            //附件上传数量
            props = {
              limit: e.regExpression ? Number(e.regExpression) : 1
            };
          }
          // ...e,
          let newObj = Object.assign(
            {
              key: 'fieldKey_' + index,
              label: e.fieldZh,
              prop: e.fieldEn,
              type: type,
              required: e.required === '是' ? true : false,
              showFlag: e.showFlag,
              fieldLinks: e.fieldLinks,
              selfFieldLink: selfFieldLink,
              nextField: nextField
            },
            {
              props: props
            }
          );
          fieldResult.push(newObj);

          if (e.controlType === 'regions') {
            //省市区+详细地址信息
            fieldResult.push({
              key: 'fieldKey_99' + index,
              label: '详细地址',
              prop: e.fieldEn + '_regionsDetail',
              type: 'input',
              required: false,
              showFlag: '否'
            });
          }
        });
        initItems.value = fieldResult;
      }
      let fieldFormResult = {};
      if (fieldResult && fieldResult.length > 0) {
        fieldResult.forEach((item) => {
          if (item.type) {
            if (
              uploadTypes.includes(item.type) ||
              regionsArrayTypes.includes(item.type) ||
              arrayTypes.includes(item.type)
            ) {
              fieldFormResult[item.prop] = [];
            }
            if (
              stringTypes.includes(item.type) ||
              selectTypes.includes(item.type)
            ) {
              fieldFormResult[item.prop] = '';
            }
          }
        });
        initModel.value = fieldFormResult;
      }
      console.log('currentData====', currentData.value);
      if (currentData.value) {
        if (fieldResult && fieldResult.length > 0) {
          fieldResult.forEach((item) => {
            // console.log("item===", item, item.prop, currentData.value)
            let setValue = null;
            let dataValue = currentData.value
              ? currentData.value[item.prop]
              : null;
            console.log('dataValue====', dataValue);
            if (dataValue) {
              if (regionsArrayTypes.includes(item.type)) {
                let regionsArr = dataValue.split(',');
                if (item.type === 'regions') {
                  if (regionsArr.length > 0 && regionsArr[3]) {
                    initItems.value.filter((f) => {
                      if (f.prop === item.prop + '_regionsDetail') {
                        initModel.value[f.prop] = regionsArr[3];
                      }
                    });
                  }
                  regionsArr.splice(regionsArr.length - 1, 1);
                  setValue = regionsArr;
                } else {
                  setValue = regionsArr;
                }
              } else if (item.type === 'imageUpload') {
                const oldFiles = dataValue
                  ? JSON.parse(dataValue).map((d, i) => {
                      return {
                        key: d.id,
                        name: d.originalFilename,
                        contentType: d.contentType,
                        url: BASE_URL + 'api/file/inline/' + d.id,
                        status: 'done'
                      };
                    })
                  : [];
                setValue = oldFiles;
              } else if (item.type === 'fileUpload') {
                const oldFiles = dataValue
                  ? JSON.parse(dataValue).map((d, i) => {
                      let newObj = {
                        key: d.id,
                        name: d.originalFilename,
                        contentType: d.contentType,
                        fileUrl: BASE_URL + 'api/file/inline/' + d.id,
                        status: 'done'
                      };
                      let mark = isImageFile(newObj);
                      newObj.isImageFile = mark;
                      if (mark) newObj.url = newObj.fileUrl;
                      return newObj;
                    })
                  : [];
                setValue = oldFiles;
              } else {
                setValue = dataValue;
              }
            }
            if (setValue) updateFormValue(item, setValue);
          });
        }
      }
    }, 200);
  };

  watch(
    () => props.currentGroup,
    (groupData) => {
      console.log(props.currentData, groupData);
      if (groupData) {
        if (groupData.infoType) {
          if (groupData.infoType === 'base') {
            queryPersonInfo();
          }
          if (groupData.mark === 'zzapply') {
            queryFormApplyFieldList();
          } else {
            queryFormTemplateField();
          }
        } else {
          queryDictionaryField(groupData);
        }
      }
    },
    {
      immediate: true
    }
  );

  defineExpose({ proFormRef });
  // export { validateHandle };
</script>
