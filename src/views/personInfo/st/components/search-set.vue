<!-- 高级查询设置 -->
<template>
  <ele-drawer
    :size="830"
    :title="routeType === 'student' ? '学生高级查询设置' : '教师高级查询设置'"
    :append-to-body="true"
    :destroy-on-close="true"
    style="max-width: 100%"
    :model-value="modelValue"
    :body-style="{ paddingBottom: '8px' }"
    @update:modelValue="updateModelValue"
  >
    <ele-page>
      <search-table-preview
        currentOpt="searchSet"
        :routeType="routeType"
        :data="currentSearchSetData"
        @DoneSearchSet="onDoneSearchSet"
      />
    </ele-page>
    <template #footer>
      <template v-if="searchSetData.length > 0">
        <ele-alert
          :closable="false"
          style="
            margin: 0 0 10px 0;
            text-align: left;
            background: unset !important;
          "
        >
          <el-link :icon="Select" type="primary" :underline="false">
            &nbsp; 已设置的高级查询
          </el-link>
          <ele-text size="md" style="margin-bottom: 8px; cursor: pointer">
            <el-col :lg="24" :md="12" :sm="12" :xs="24">
              <el-tag
                v-for="(data, index) in searchSetData"
                :key="index"
                @click="onClickSearch(data)"
                @close="removeOpt(data)"
                closable
                size="medium"
                :effect="currentSearchSetData.id === data.id ? 'dark' : 'plain'"
                style="margin: 10px 10px 10px 0"
              >
                {{ data.name }}
              </el-tag>
            </el-col>
          </ele-text>
        </ele-alert>
        <el-divider style="margin: 0 0 26px 0; opacity: 0.6" />
      </template>

      <el-button @click="updateModelValue(false)">取消</el-button>
      <el-button type="primary" :loading="loading" @click="onSaveSearch">
        保存查询条件并查询
      </el-button>
      <el-button type="primary" :loading="loading" @click="onSearch">
        查询
      </el-button>
    </template>
  </ele-drawer>
</template>

<script setup>
  import { ref, unref, watch } from 'vue';
  import { EleMessage } from 'ele-admin-plus/es';
  import { Select } from '@element-plus/icons-vue';
  import SearchTablePreview from '../components/search-table-preview.vue';
  import {
    operation,
    queryQuickSearchInfo,
    removes
  } from '../api/search-index';
  import { useRouter } from 'vue-router';
  import { ElMessageBox } from 'element-plus';

  const emit = defineEmits(['done', 'update:modelValue']);

  const { currentRoute, push } = useRouter();
  const { params, path } = unref(currentRoute);
  const routeType = path.split('/')[3];

  const props = defineProps({
    /** 弹窗是否打开 */
    modelValue: Boolean,
    /** 修改回显的数据 */
    data: Object
  });

  /** 是否是修改 */
  const isUpdate = ref(false);

  /** 提交状态 */
  const loading = ref(false);
  const formData = ref({});
  const searchSetData = ref([]);
  const currentSearchSetData = ref([]);

  /** 保存编辑 */
  const onSaveSearch = () => {
    if (!formData.value?.name) {
      EleMessage.error('请设置查询记录的名称');
      return;
    }
    if (!formData.value?.condition) {
      EleMessage.error('请设置查询参数');
      return;
    }
    loading.value = true;
    operation(formData.value)
      .then((msg) => {
        loading.value = false;
        EleMessage.success(msg);
        emit('done', formData.value?.condition);
        updateModelValue(false);
      })
      .catch((e) => {
        loading.value = false;
        EleMessage.error(e.message);
      });
  };

  /** 删除 */
  const removeOpt = (row) => {
    const rows = [row];
    ElMessageBox.confirm(
      '确定要删除“' + rows.map((d) => d.name).join(', ') + '”吗?',
      '系统提示',
      { type: 'warning', draggable: true }
    )
      .then(() => {
        const loading = EleMessage.loading('请求中..');
        removes(rows.map((d) => d.id))
          .then((msg) => {
            loading.close();
            EleMessage.success(msg);
            queryQuickSearch();
            // reload();
          })
          .catch((e) => {
            loading.close();
            EleMessage.error(e.message);
          });
      })
      .catch(() => {});
  };

  const onSearch = () => {
    if (!formData.value?.name) {
      EleMessage.error('请设置查询记录的名称');
      return;
    }
    if (!formData.value?.condition) {
      EleMessage.error('请设置查询参数');
      return;
    }
    emit('done', formData.value?.condition);
    updateModelValue(false);
  };

  /** 点击选择具体的高级查询记录值*/
  const onClickSearch = (value) => {
    currentSearchSetData.value = value;
  };

  /** 设置高级查询记录值 */
  const onDoneSearchSet = (value) => {
    formData.value = value;
  };

  /** 更新modelValue */
  const updateModelValue = (value) => {
    currentSearchSetData.value = [];
    formData.value = [];
    emit('update:modelValue', value);
  };

  /** 查询已设置的高级查询记录*/
  const queryQuickSearch = async () => {
    searchSetData.value = await queryQuickSearchInfo();
  };

  watch(
    () => props.modelValue,
    (modelValue) => {
      if (modelValue) {
        queryQuickSearch();
      }
    }
  );
</script>
