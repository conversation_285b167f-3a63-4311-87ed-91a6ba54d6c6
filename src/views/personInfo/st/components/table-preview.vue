<template>
  <ele-card
    :body-style="{ height: 'auto', padding: '10px 5px 10px 5px!important' }"
    :header="currentGroup.title"
  >
    <template #extra>
      <el-link
        v-if="currentGroup && !currentGroup.infoType"
        :icon="PlusOutlined"
        type="primary"
        :underline="false"
        @click="add"
        >新增
      </el-link>
    </template>
    <el-form
      ref="formRef"
      size="small"
      :model="form"
      label-width="0px"
      @submit.prevent=""
    >
      <div style="overflow: auto">
        <ele-table size="small" style="min-width: 580px; table-layout: fixed">
          <colgroup>
            <col width="40px" />
            <col v-for="header in initTableHeader" />
            <col width="100px" />
          </colgroup>
          <thead>
            <tr>
              <th style="position: sticky; left: 0; z-index: 98"></th>
              <th v-for="header in initTableHeader">{{ header.label }}</th>
              <th
                :style="{
                  textAlign: 'center',
                  position: 'sticky',
                  right: 0,
                  zIndex: 98
                }"
              >
                操作
              </th>
            </tr>
          </thead>
          <tbody>
            <tr
              v-for="(row, index) in form.users"
              :key="row.key"
              style="height: auto"
            >
              <td
                :style="{
                  textAlign: 'center',
                  position: 'sticky',
                  left: 0,
                  zIndex: 98
                }"
              >
                {{ index + 1 }}
              </td>
              <td v-for="item in initItems">
                <TableFormItem
                  v-if="row.isEdit"
                  :item="item"
                  :index="index"
                  :model="row"
                  @updateValue="(value) => updateValue(index, item.prop, value)"
                >
                  <template
                    v-for="name in Object.keys($slots).filter(
                      (k) =>
                        ![
                          'default',
                          'footer',
                          'topExtra',
                          'bottomExtra'
                        ].includes(k)
                    )"
                    #[name]="slotProps"
                  >
                    <slot :name="name" v-bind="slotProps || {}"></slot>
                  </template>
                </TableFormItem>
                <div v-else class="editable-cell-text">{{
                  row[item.prop]
                }}</div>
              </td>
              <td
                :style="{
                  textAlign: 'center',
                  position: 'sticky',
                  right: 0,
                  zIndex: 98
                }"
              >
                <div style="display: inline; align-items: center">
                  <el-link
                    v-if="row.isEdit"
                    type="success"
                    :underline="false"
                    @click="done(row)"
                  >
                    完成
                  </el-link>
                  <el-link
                    v-else
                    type="primary"
                    :underline="false"
                    @click="edit(row)"
                  >
                    编辑
                  </el-link>
                  <el-divider direction="vertical" style="margin: 0 8px" />
                  <el-link
                    type="danger"
                    :underline="false"
                    @click="remove(row, index)"
                  >
                    删除
                  </el-link>
                </div>
              </td>
            </tr>
            <tr v-if="!form.users || !form.users.length">
              <td
                :colspan="initTableHeader.length + 2"
                style="text-align: center"
              >
                <ele-text style="padding: 4px 0" type="secondary">
                  暂无数据
                </ele-text>
              </td>
            </tr>
          </tbody>
        </ele-table>
      </div>
    </el-form>
  </ele-card>
</template>

<script setup>
  import { ref, watch } from 'vue';
  import { EleMessage } from 'ele-admin-plus/es';
  import { PlusOutlined } from '@/components/icons';
  import { getFieldList } from '@/views/personInfo/st/api';
  import { getUserListInfo, operation, removes } from '../api/table-index';
  import {
    arrayTypes,
    selectTypes,
    stringTypes
  } from '@/components/ProForm/util';
  import TableFormItem from '@/components/ProForm/components/table-form-item.vue';
  import { useFormData } from '@/utils/use-form-data';
  import { getFormTemplateField } from '@/views/zizhu/api/form-template-field-index.js';
  import { getDictionaryField } from '@/views/system/sphfw/dictionary-field/api/index.js';
  import {
    getDicListGroupField,
    getFormApplyFieldList
  } from '@/views/zizhu/apply/api/index.js';

  const props = defineProps({
    /** 表单标题宽度 */
    labelWidth: Number,
    /** 表单显示列数 */
    grid: Number,
    currentGroup: Object,
    routeType: String,
    xgh: String,
    data: Object,
    isAddMark: Number
  });

  /** 表单实例 */
  const formRef = ref(null);

  /** 表单数据 */
  const [form, resetFields] = useFormData({
    users: []
  });

  /** 添加 */
  const add = () => {
    form.users.push({
      key: Date.now() + '-' + form.users.length,
      ...initModel.value,
      isEdit: true
    });
  };

  /** 删除 */
  const remove = (_row, index) => {
    form.users.splice(index, 1);
    if (_row.id) {
      removes(props.routeType, [_row.id])
        .then((msg) => {
          loading.value = false;
          EleMessage.success(msg);
        })
        .catch((e) => {
          loading.value = false;
          EleMessage.error(e.message);
        });
    }
  };

  /** 表单提交 */
  const submit = (row) => {
    formRef.value?.validate?.((valid, obj) => {
      if (!valid) {
        const errors = obj ? Object.keys(obj).length : 0;
        EleMessage.error(`有 ${errors} 项校验不通过`);
        return;
      } else {
        loading.value = true;
        let obj = {
          xgh: props.xgh,
          userType: props.routeType,
          groupId: props.currentGroup.id,
          groupName: props.currentGroup.title,
          ...row
        };
        operation(props.routeType, obj)
          .then((msg) => {
            loading.value = false;
            EleMessage.success(msg);
            row.isEdit = false;
          })
          .catch((e) => {
            loading.value = false;
            EleMessage.error(e.message);
          });
      }
    });
  };

  /** 更新值 */
  const updateValue = (index, prop, value) => {
    form.users[index][prop] = value;
  };

  /** 完成 */
  const done = (row) => {
    submit(row);
  };

  /** 编辑 */
  const edit = (row) => {
    row.isEdit = true;
  };

  /** 字段 */
  const fieldData = ref([]);
  /** 请求状态 */
  const loading = ref(true);

  /** 表单项 */
  const initItems = ref([]);
  /** 获取表单字段 */
  const initModel = ref([]);
  const initTableHeader = ref([]);

  const queryDictionaryField = (group) => {
    loading.value = true;
    getFieldList(props.routeType, { groupId: group.id })
      .then((list) => {
        loading.value = false;
        fieldData.value = list;
      })
      .catch((e) => {
        EleMessage.error(e.message);
      });
  };

  const queryUserListInfo = (group) => {
    loading.value = true;
    let obj = {
      groupId: group.id,
      groupName: group.groupName,
      xgh: props.xgh
    };
    getUserListInfo(props.routeType, obj)
      .then((list) => {
        loading.value = false;
        form.users = list;
      })
      .catch((e) => {
        EleMessage.error(e.message);
      });
  };

  watch(
    () => fieldData.value,
    (fieldData) => {
      let fieldResult = [];
      initTableHeader.value = [];
      if (fieldData && fieldData.length > 0) {
        fieldData.forEach((e, index) => {
          let tableColumns = {
            columnKey: e.fieldEn,
            label: e.fieldZh,
            minWidth: 120
            // slot: 'name'
          };
          initTableHeader.value.push(tableColumns);

          let props = {};
          let type = e.controlType;
          if (e.controlType === 'select') {
            if (e.loadDataType && e.loadDataType !== 'other') {
              type = 'dictSelect';
              props = {
                code: e.loadDataType,
                filterable: true
              };
            } else {
              type = 'dictSelect';
              props = {
                code: e.loadDataType,
                filterable: true,
                dicQueryParams: {
                  dictFieldUrl: e.loadDataUrl
                }
              };
            }
          }
          let newObj = Object.assign(
            {
              key: 'fieldKey_' + index,
              editable: true,
              label: e.fieldZh,
              prop: e.fieldEn,
              type: type,
              required: e.required === '是' ? true : false
            },
            {
              props: props
            }
          );
          fieldResult.push(newObj);
        });
        initItems.value = fieldResult;
      }

      let fieldFormResult = {};
      if (fieldResult && fieldResult.length > 0) {
        fieldResult.forEach((item) => {
          if (item.type) {
            // if ('sliderRange' === item.type) {
            //     fieldResult[item.prop] = [30, 60];
            // }
            if (arrayTypes.includes(item.type)) {
              fieldFormResult[item.prop] = [];
            }
            if (
              stringTypes.includes(item.type) ||
              selectTypes.includes(item.type)
            ) {
              fieldFormResult[item.prop] = '';
            }
          }
        });
        initModel.value = fieldFormResult;
      }
    }
  );

  /** 资助申请查询组的申请字段信息 */
  const queryFormTemplateField = () => {
    loading.value = true;
    getFormTemplateField({
      projectId: props.currentGroup.projectId,
      groupId: props.currentGroup.id,
      type: props.routeType
    })
      .then((list) => {
        loading.value = false;
        fieldData.value = list ?? [];
        console.log(list);
      })
      .catch((e) => {
        loading.value = false;
        EleMessage.error(e.message);
      });
  };
  /**资助项目申请- 查询组的申请字段信息 */
  const queryFormApplyFieldList = () => {
    loading.value = true;
    getFormApplyFieldList(props.currentGroup.projectId, {
      projectId: props.currentGroup.projectId,
      groupId: props.currentGroup.id,
      type: props.routeType
    })
      .then((list) => {
        loading.value = false;
        // if (list) {
        //   list.forEach((item) => {
        //     item.showFlag = 'readonly'//预览只读
        //   })
        // }
        fieldData.value = list ?? [];
        console.log('查询组的申请字段信息===', list);
      })
      .catch((e) => {
        loading.value = false;
        EleMessage.error(e.message);
      });
  };

  /** 学生/角色基础字段信息 */
  const queryDicField = () => {
    loading.value = true;
    getDictionaryField({
      groupId: props.currentGroup.dicGroupId
    })
      .then((list) => {
        loading.value = false;
        fieldData.value = list ?? [];
      })
      .catch((e) => {
        loading.value = false;
        EleMessage.error(e.message);
      });
  };

  /** 资助项目申请- 学生/角色基础字段信息 */
  const queryDicListGroupField = () => {
    loading.value = true;
    getDicListGroupField(
      props.currentGroup.projectId,
      props.currentGroup.dicGroupId
    )
      .then((list) => {
        loading.value = false;
        fieldData.value = list ?? [];
      })
      .catch((e) => {
        loading.value = false;
        EleMessage.error(e.message);
      });
  };

  watch(
    () => props.currentGroup,
    (groupData) => {
      if (groupData) {
        if (groupData.infoType) {
          if (groupData.dicGroupId) {
            if (groupData.mark === 'zzapply') {
              queryDicListGroupField();
            } else {
              queryDicField();
            }
          } else {
            if (groupData.mark === 'zzapply') {
              queryFormApplyFieldList();
            } else {
              queryFormTemplateField();
            }
          }
        } else {
          queryDictionaryField(groupData);
          if (props.xgh) queryUserListInfo(groupData);
        }
      }
    },
    {
      immediate: true
    }
  );
</script>
<style lang="scss" scoped>
  /* 表单验证气泡形式 */
  .form-error-popper.el-form-item > :deep(.el-form-item__content) {
    & > .el-form-item__error {
      position: absolute;
      left: 0;
      top: calc(0px - 100% - 6px);
      width: max-content;
      color: #fff;
      font-size: 12px;
      background: var(--el-color-danger);
      transition: all 0.2s;
      padding: 10px;
      border-radius: 4px;
      z-index: 999;
      opacity: 0;
      visibility: hidden;
      pointer-events: none;

      &:after {
        content: '';
        border: 6px solid transparent;
        border-top-color: var(--el-color-danger);
        position: absolute;
        left: 12px;
        bottom: -11px;
      }
    }

    &:hover > .el-form-item__error {
      opacity: 1;
      visibility: visible;
      pointer-events: all;
    }
  }
</style>
