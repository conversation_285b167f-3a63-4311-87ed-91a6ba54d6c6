<template>
  <el-form ref="formRef" size="small" :model="form" @submit.prevent="">
    <el-row>
      <el-col :lg="16" :md="8" :sm="24" :xs="24">
        <el-form-item label="设置名称">
          <el-input
            clearable
            required
            v-model.trim="form.name"
            placeholder="请设置查询记录的名称"
          />
        </el-form-item>
      </el-col>
      <el-col :lg="8" :md="8" :sm="24" :xs="24">
        <el-form-item label=" " lable-width="30px" prop="selectorDatas">
          <div
            style="margin-bottom: 6px; cursor: pointer; margin-left: 154px"
            @click="add"
          >
            <el-link :icon="PlusOutlined" type="primary" :underline="false"
              >新增查询设置</el-link
            >
          </div>
        </el-form-item>
      </el-col>
    </el-row>
    <div style="overflow: auto">
      <ele-table
        :border="true"
        size="small"
        style="min-width: 580px; table-layout: fixed"
      >
        <colgroup>
          <col width="40px" />
          <col v-for="header in initTableHeader" />
          <col width="100px" />
        </colgroup>
        <thead>
          <tr>
            <th style="position: sticky; left: 0; z-index: 98"></th>
            <th v-for="header in initTableHeader">{{ header.label }}</th>
            <th
              :style="{
                textAlign: 'center',
                position: 'sticky',
                right: 0,
                zIndex: 98
              }"
            >
              操作
            </th>
          </tr>
        </thead>
        <tbody>
          <tr
            v-for="(row, index) in form.users"
            :key="row.key"
            style="height: 38px"
          >
            <td
              :style="{
                textAlign: 'center',
                position: 'sticky',
                left: 0,
                zIndex: 98
              }"
            >
              {{ index + 1 }}
            </td>
            <td v-for="item in initItems[index]">
              <template v-if="item.type === 'dictSearchSet'">
                <dict-data
                  v-if="row.isEdit"
                  type="searchSet"
                  filterable
                  :dicQueryParams="{ routeType: routeType }"
                  code="searchSet"
                  :model-value="row[item.prop]"
                  @update:modelValue="
                    (value) => updateValuefield(index, item, value)
                  "
                />
                <div v-else class="editable-cell-text">{{
                  row[item.prop]
                }}</div>
              </template>
              <template v-else>
                <TableFormItem
                  v-if="row.isEdit"
                  :item="item"
                  :index="index"
                  :model="row"
                  @updateValue="(value) => updateValue(index, item, value)"
                >
                  <template
                    v-for="name in Object.keys($slots).filter(
                      (k) =>
                        ![
                          'default',
                          'footer',
                          'topExtra',
                          'bottomExtra'
                        ].includes(k)
                    )"
                    #[name]="slotProps"
                  >
                    <slot :name="name" v-bind="slotProps || {}"></slot>
                  </template>
                </TableFormItem>
                <div v-else class="editable-cell-text">{{
                  row[item.prop]
                }}</div>
              </template>
            </td>
            <td
              :style="{
                textAlign: 'center',
                position: 'sticky',
                right: 0,
                zIndex: 98
              }"
            >
              <div style="display: inline; align-items: center">
                <el-link
                  type="danger"
                  :underline="false"
                  @click="remove(row, index)"
                >
                  删除
                </el-link>
              </div>
            </td>
          </tr>
          <tr v-if="!form.users || !form.users.length">
            <td
              :colspan="initTableHeader.length + 2"
              style="text-align: center"
            >
              <ele-text style="padding: 4px 0" type="secondary">
                暂无数据
              </ele-text>
            </td>
          </tr>
        </tbody>
      </ele-table>
    </div>
  </el-form>
</template>

<script setup>
  import { nextTick, ref, watch } from 'vue';
  import { PlusOutlined } from '@/components/icons';
  import TableFormItem from '@/components/ProForm/components/table-form-item.vue';
  import { useFormData } from '@/utils/use-form-data';

  const emit = defineEmits(['DoneSearchSet']);

  const props = defineProps({
    /** 表单标题宽度 */
    labelWidth: Number,
    /** 表单显示列数 */
    grid: Number,
    currentGroup: Object,
    routeType: String,
    xgh: String,
    data: Object,
    currentOpt: Object
  });

  /** 表单实例 */
  const formRef = ref(null);
  const fieldResult = ref([]);

  /** 表单数据 */
  const [form, resetFields] = useFormData({
    id: void 0,
    name: '',
    users: []
  });

  /** 删除 */
  const remove = (_row, index) => {
    form.users.splice(index, 1);
    initItems.value.splice(index, 1);
  };

  /** 查询字段更新值 */
  const updateValuefield = (index, item, e) => {
    // form.users[index][item.prop] = e.dictDataName;
    form.users[index][item.prop] = e.dictDataCode;
    if (e.controlType) {
      let type = e.controlType;
      initItems.value[index].filter((f) => {
        if (f.prop === 'fieldValue') {
          form.users[index][f.prop] = null;
          let props = {};
          if (e.controlType === 'select') {
            if (e.loadDataType && e.loadDataType !== 'other') {
              type = 'dictSelect';
              props = {
                code: e.loadDataType,
                refresh: e.dictDataName,
                dicQueryParams: {
                  getValType: e.loadDataType !== 'pycc' ? 'name' : ''
                },
                filterable: true
              };
            } else {
              //院系专业班级页面逻辑处理
              type = 'dictSelect';
              props = {
                code: e.loadDataType,
                refresh: e.dictDataName,
                filterable: true,
                dicQueryParams: {
                  dictFieldUrl: e.loadDataUrl,
                  valueField: e.valueField,
                  textField: e.textField
                }
              };
            }
          }
          f.type = type;
          f.props = props;
        }
      });
      form.users[index]['field'] = e;
    }
    emit('DoneSearchSet', {
      id: form.id,
      name: form.name,
      condition: form.users.length > 0 ? JSON.stringify(form.users) : ''
    });
  };

  /** 更新值 */
  const updateValue = (index, item, value) => {
    form.users[index][item.prop] = value;
    if (props.currentOpt === 'searchSet') {
      emit('DoneSearchSet', {
        id: form.id,
        name: form.name,
        condition: form.users.length > 0 ? JSON.stringify(form.users) : ''
      });
    }
  };

  watch(
    () => form.name,
    (name) => {
      if (name) {
        emit('DoneSearchSet', {
          id: form.id,
          name: name,
          condition: form.users.length > 0 ? JSON.stringify(form.users) : ''
        });
      }
    }
  );

  // /** 编辑 */
  // const edit = (row) => {
  //     row.isEdit = true;
  // };

  /** 请求状态 */
  const loading = ref(true);

  /** 表单项 */
  const initItems = ref([]);
  /** 获取表单字段 */
  const initModel = ref([]);
  const initTableHeader = ref([]);

  watch(
    () => props.currentOpt,
    (newVal) => {
      if (newVal) {
        initTableHeader.value.push(
          {
            columnKey: 'fieldName',
            label: '查询字段',
            minWidth: 120,
            type: 'dictSearchSet',
            props: {
              code: newVal,
              filterable: true,
              dicQueryParams: { routeType: props.routeType }
            }
          },
          {
            columnKey: 'fieldValue',
            label: '条件值',
            minWidth: 120,
            type: 'input'
          },
          {
            columnKey: 'queryType',
            label: '条件',
            minWidth: 120,
            type: 'select',
            options: [
              { label: '等于', value: 'EQ' },
              { label: '不等于', value: 'NE' },
              { label: '大于', value: 'GT' },
              { label: '大于等于', value: 'GE' },
              { label: '小于', value: 'LT' },
              { label: '小于等于', value: 'LE' },
              { label: '包含', value: 'LIKE' },
              { label: '不包含', value: 'NOT_LIKE' },
              { label: '结尾等于', value: 'LIKE_LEFT' },
              { label: '开头等于', value: 'LIKE_RIGHT' },
              { label: '为NULL', value: 'IS_NULL' },
              { label: '不为空', value: 'IS_NOT_NULL' },
              { label: 'IN', value: 'IN' },
              { label: 'NOT IN', value: 'NOT_IN' },
              { label: 'IN条件解析逗号分割', value: 'IN_STR' },
              { label: ' NOT IN条件解析逗号分割', value: 'NOT_IN_STR' }
            ]
          },
          {
            columnKey: 'joinRelation',
            label: '关联符',
            minWidth: 120,
            type: 'select',
            options: [
              { label: '并且', value: 'AND' },
              { label: '或者', value: 'OR' }
            ]
          }
        );
        let fieldRes = [];
        initTableHeader.value.forEach((e, index) => {
          let newObj = Object.assign({
            key: 'fieldKey_' + index,
            editable: true,
            label: e.label,
            prop: e.columnKey,
            type: e.type,
            options: e.options ? e.options : [],
            props: e.props ? e.props : {}
          });
          fieldRes.push(newObj);
        });
        fieldResult.value = fieldRes;
        // initItems.value = fieldResult
      }
    },
    {
      immediate: true
    }
  );

  /** 添加 */
  const add = () => {
    form.users.push({
      key: Date.now() + '-' + form.users.length,
      ...initModel.value,
      isEdit: true
    });
    initItems.value.push(fieldResult.value);
  };

  watch(
    () => props.data,
    (data) => {
      if (data) {
        form.id = data.id;
        form.name = data.name;
        let condition = data.condition ? JSON.parse(data.condition) : [];
        form.users = condition;
        // for (let i = 0; i < condition.length; i++) {
        //     initItems.value.push(fieldResult.value)
        // }
        nextTick(() => {
          condition.forEach((user, index) => {
            let e = user.field;
            let type = e.controlType;
            if (type) {
              let newData = [];
              fieldResult.value.forEach((f, i) => {
                let fvalProps = {};
                if (f.prop === 'fieldValue') {
                  if (type === 'select') {
                    if (e.loadDataType && e.loadDataType !== 'other') {
                      f.type = 'dictSelect';
                      fvalProps = {
                        code: e.loadDataType,
                        refresh: e.dictDataName,
                        filterable: true,
                        dicQueryParams: {
                          getValType: e.loadDataType !== 'pycc' ? 'name' : ''
                        }
                      };
                    } else {
                      //院系专业班级页面逻辑处理
                      f.type = 'dictSelect';
                      fvalProps = {
                        code: e.loadDataType,
                        refresh: e.dictDataName,
                        filterable: true,
                        dicQueryParams: {
                          dictFieldUrl: e.loadDataUrl,
                          valueField: e.valueField,
                          textField: e.textField
                        }
                      };
                    }
                  }
                }
                newData.push({
                  ...f,
                  props: fvalProps
                });
              });
              initItems.value[index] = newData;
            }
          });
        });
      }
    },
    {
      immediate: true
    }
  );
</script>
