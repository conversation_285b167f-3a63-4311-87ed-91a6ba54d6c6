<template>
  <ele-page hide-footer flex-table>
    <ele-split-panel
      ref="splitRef"
      space="6px"
      size="84%"
      :custom-style="{ borderWidth: '0 1px 0 0', width: '100%' }"
    >
      <div
        ref="containerRef"
        :style="{ height: pageHeight - 36 + 'px', overflow: 'auto' }"
      >
        <div v-for="(group, index) in groupData">
          <div :id="'anchor' + index">
            <table-preview
              v-if="group.listFlag === '是'"
              :key="'formKey' + index"
              :labelWidth="config.labelWidth"
              :grid="config.grid"
              :routeType="routeType"
              :xgh="xgh"
              :currentGroup="group"
            />
            <form-preview
              v-else
              :ref="setChildComponentRef"
              :key="'formKey' + index"
              :labelWidth="config.labelWidth"
              :grid="config.grid"
              :routeType="routeType"
              :currentData="currentData"
              :currentGroup="group"
              @onDoneGroup="onDoneGroup"
              @onDoneDeleteFile="onDoneDeleteFile"
            />
          </div>
        </div>
      </div>
      <!-- 底部工具栏 -->
      <ele-bottom-bar>
        <ele-text v-if="validMsg" type="danger" :icon="CloseCircleOutlined">
          <span>{{ validMsg }}</span>
        </ele-text>
        <template #extra>
          <el-button size="small" @click="onBack">取消</el-button>
          <el-button
            size="small"
            type="primary"
            :loading="loading"
            @click="onSubmit"
          >
            提交
          </el-button>
        </template>
      </ele-bottom-bar>
      <template #body>
        <ele-card :body-style="{ padding: '10px' }">
          <!--                    <div class="info-user">-->
          <!--                        <div class="info-user-avatar" @click="openCropper">-->
          <!--                            <el-avatar :size="100" :src="resData.avatar"/>-->
          <!--                            <el-icon class="info-user-avatar-icon">-->
          <!--                                <CloudUploadOutlined style="stroke-width: 3"/>-->
          <!--                            </el-icon>-->
          <!--                        </div>-->
          <!--                    </div>-->
          <el-anchor
            :container="containerRef"
            direction="vertical"
            :offset="70"
            @click="handleClick"
          >
            <el-anchor-link
              v-for="(group, index) in groupData"
              :href="'#anchor' + index"
              :key="index"
              :title="group.title"
            />
          </el-anchor>
        </ele-card>
      </template>
    </ele-split-panel>

    <!-- 头像裁剪弹窗 -->
    <!--        <ele-cropper-modal v-model="visible"-->
    <!--                           :src="resData.avatar"-->
    <!--                           toBlob="true"-->
    <!--                           :options="{-->
    <!--                                aspectRatio: 1,-->
    <!--                                autoCropArea: 1,-->
    <!--                                viewMode: 1,-->
    <!--                                dragMode: 'move',-->
    <!--                           }"-->
    <!--                           :modal-props="{ destroyOnClose: true }"-->
    <!--                           @done="onCrop"/>-->
  </ele-page>
</template>

<script setup>
  import { onMounted, reactive, ref, unref } from 'vue';
  import { useRoute, useRouter } from 'vue-router';
  import { EleMessage } from 'ele-admin-plus/es';
  import { toFormData } from '@/utils/common';
  import { usePageTab } from '@/utils/use-page-tab';
  import { getGroupList, getPersonInfo, operation } from '../api/index';
  import FormPreview from '../components/form-preview.vue';
  import TablePreview from '../components/table-preview.vue';
  import { CloseCircleOutlined } from '@/components/icons';
  import { mapState } from '@/plugins/lib.js';

  let { sharedZizhuData } = mapState();

  const route = useRoute();

  const { currentRoute, push } = useRouter();
  const { removePageTab, getRouteTabKey, setPageTabTitle } = usePageTab();

  const { path } = unref(currentRoute);

  let xgh = route.params?.id ?? '';
  let routeType = path.split('/')[4];

  const containerRef = ref(null);
  const handleClick = (e, link) => {
    e.preventDefault();
  };

  /** 表单页面回显数据 */
  const currentData = ref({});
  /** 表单配置 */
  const config = reactive({
    labelWidth: 120,
    grid: 3
  });

  /** 是否显示裁剪弹窗 */
  const visible = ref(false);

  /** 打开图片裁剪 */
  const openCropper = () => {
    visible.value = true;
  };

  /** 存放form提交字段*/
  const proFormGroup = ref([]);
  const onDoneGroup = (data) => {
    // proFormRef.value = data.proFormRef;
    let isExist = proFormGroup.value.filter(
      (obj) => obj.groupId === data.groupId
    );
    if (isExist.length > 0) {
      proFormGroup.value.filter((obj) => {
        if (obj.groupId === data.groupId) obj.values = data.values;
      });
    } else {
      proFormGroup.value.push(data);
    }
  };

  /** 存放删除的图片ID，附件ID*/
  const proFormDelFile = ref([]);

  const onDoneDeleteFile = (data) => {
    let isExist = proFormDelFile.value.filter((obj) => obj.prop === data.prop);
    if (isExist.length > 0) {
      proFormDelFile.value.filter((obj) => {
        if (obj.prop === data.prop) obj.values = data.values;
      });
    } else {
      proFormDelFile.value.push(data);
    }
  };

  /** 头像裁剪完成回调 */
  const onCrop = (result) => {
    visible.value = false;
    // emit('done', {avatar: result});
    /* const loading = EleMessage.loading('请求中..');
  updateUserInfo({ avatar: result })
    .then((data) => {
      loading.close();
      visible.value = false;
      EleMessage.success('修改成功');
      emit('done', data);
    })
    .catch((e) => {
      loading.close();
      EleMessage.error(e.message);
    }); */
  };
  const childComponentsRefs = ref([]);

  // 设置子组件的引用
  const setChildComponentRef = (el) => {
    if (el) {
      childComponentsRefs.value.push(el);
    }
  };

  /** 表单验证失败提示信息 */
  const validMsg = ref('');
  const validMsgCount = ref(0);
  /** 存放子组件的数组 */
  let resultArr = reactive([]);
  /** 用来创建 Promise 实例，为多个组件校验使用 */
  const checkForm = (formChild) => {
    let result = new Promise((resolve, reject) => {
      formChild.proFormRef?.validate((valid, obj) => {
        if (valid) {
          resolve(true);
        } else {
          const errors = obj ? Object.keys(obj).length : 0;
          validMsgCount.value += errors;
          resolve(false);
          reject();
        }
      });
    });
    resultArr.push(result);
  };

  const onSubmit = async () => {
    // 遍历表单数组，依次对每个表单进行校验
    await childComponentsRefs.value.map((child) => {
      checkForm(child);
    });
    try {
      const results = await Promise.all(resultArr);
      // 检查所有结果是否为true
      const allTrue = results.every(Boolean);
      resultArr = []; //每次请求完要清空数组
      if (allTrue) {
        // 执行后续操作
        save();
      } else {
        validMsg.value = ` 共有 ${validMsgCount.value} 项校验不通过`;
        validMsgCount.value = 0;
      }
    } catch (error) {
      console.error('有异步操作失败:', error);
    }
  };

  /** 提交 */
  const save = () => {
    let resData = proFormGroup.value;
    let result = {};
    resData.forEach((res) => {
      const value = res.values;
      Object.keys(value).forEach((key) => {
        const resValue = value[key];
        if (resValue) result[key] = resValue;
      });
    });

    Object.keys(result).forEach((key) => {
      //处理type=regions类型，省市区+详细地址逻辑处理
      if (key.includes('_regionsDetail')) {
        let keyArray = key.split('_');
        result[keyArray[0]] = [result[keyArray[0]].join(), result[key]].join(
          ','
        );
      }
    });

    let delFileIdData = proFormDelFile.value;
    let resultFile = {};
    delFileIdData.forEach((res) => {
      resultFile[res.prop] = res.values;
    });
    if (resultFile) result['deleteFileIds'] = JSON.stringify(resultFile);
    let data = toFormData({ ...result });
    loading.value = true;
    operation(routeType, data)
      .then((msg) => {
        loading.value = false;
        EleMessage.success(msg);
        onBack();
      })
      .catch((e) => {
        loading.value = false;
        EleMessage.error(e.message);
      });
  };

  /** 用户信息 */
  const form = reactive({});

  /** 组 */
  const groupData = ref([]);
  /** 当前组 */
  const currentGroup = ref();

  /** 请求状态 */
  const loading = ref(true);

  const isAddMark = ref(0);

  /** table组件新增 */
  const onClickAllTable = () => {
    isAddMark.value++;
  };
  /** 返回 */
  const onBack = () => {
    removePageTab({ key: getRouteTabKey() });
    push('/personInfo/st/' + routeType);
  };

  /** 查询详情 */
  const queryDictionaryGroup = () => {
    if (xgh && xgh !== 'INSERT') queryPersonInfo();
    setTimeout(() => {
      loading.value = true;
      getGroupList(routeType)
        .then((list) => {
          loading.value = false;
          if (list.length > 0) {
            list.forEach((e, index) => {
              e.data = currentData.value;
              e.index = e.id;
              e.title = e.groupName;
              delete e.icon;
            });
          }
          groupData.value = list;
          currentGroup.value = list[0];
          onMenuItemClick(list[0]);
        })
        .catch((e) => {
          EleMessage.error(e.message);
        });
    }, 500);
  };
  /** 根据学工号查询 */
  const queryPersonInfo = () => {
    loading.value = true;
    getPersonInfo(routeType, xgh)
      .then((list) => {
        loading.value = false;
        list['avatar'] = null;
        currentData.value = list;
        let title =
          routeType === 'student'
            ? list.xm + '-成长档案'
            : list.xm + '-教师详情';
        setPageTabTitle(title);
      })
      .catch((e) => {
        EleMessage.error(e.message);
      });
  };

  /** 选中 */
  const active = ref();

  /** 菜单点击事件 */
  const onMenuItemClick = (item) => {
    currentGroup.value = item;
    active.value = item.index;
  };

  const pageHeight = ref(0);
  onMounted(() => {
    // 获取页面高度
    pageHeight.value =
      document.querySelector('.ele-admin-content').clientHeight - 14;
  });

  queryDictionaryGroup();
</script>

<script>
  export default {
    name: 'PersonInfoStDetails'
  };
</script>

<style lang="scss" scoped>
  .info-user {
    padding-top: 8px;
    box-sizing: border-box;
    text-align: center;

    .info-user-avatar {
      display: inline-block;
      position: relative;
      cursor: pointer;
      line-height: 0;

      .info-user-avatar-icon {
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        color: #fff;
        font-size: 30px;
        display: none;
        z-index: 2;
      }

      &::after {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        border-radius: 50%;
        background-color: transparent;
        transition: background-color 0.3s;
      }

      &:hover {
        .info-user-avatar-icon {
          display: block;
        }

        &::after {
          background-color: rgba(0, 0, 0, 0.4);
        }
      }
    }
  }
</style>
