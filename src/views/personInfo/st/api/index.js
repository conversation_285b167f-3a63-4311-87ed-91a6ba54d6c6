import request from '@/utils/request';

/**
 * 导入
 */
export async function importData(file, routeType) {
  const formData = new FormData();
  formData.append('file', file);
  // formData.append('codeType', codeType);
  const res = await request.post(
    '/personInfo/' + routeType + '/importData',
    formData
  );
  if (res.data.code === 0 || res.data.code === 2) {
    return res.data;
  }
  return Promise.reject(new Error(res.data.message));
}

/**
 * 批量更新
 */
export async function importUpdateData(file, routeType) {
  const formData = new FormData();
  formData.append('file', file);
  // formData.append('codeType', codeType);
  const res = await request.post(
    '/personInfo/' + routeType + '/importUpdateData',
    formData
  );
  if (res.data.code === 0 || res.data.code === 2) {
    return res.data;
  }
  return Promise.reject(new Error(res.data.message));
}

/**
 * 批量上传照片
 */
export async function uploadPhoto(file, routeType) {
  const formData = new FormData();
  formData.append('file', file);
  // formData.append('codeType', codeType);
  const res = await request.post(
    '/personInfo/' + routeType + '/uploadPhoto',
    formData
  );
  if (res.data.code === 0 || res.data.code === 2) {
    return res.data;
  }
  return Promise.reject(new Error(res.data.message));
}

/**
 * 照片打包下载
 */
export async function downloadPhoto(file, routeType) {
  const formData = new FormData();
  formData.append('file', file);
  // formData.append('codeType', codeType);
  const res = await request.post(
    '/personInfo/' + routeType + '/downloadPhoto',
    formData
  );
  if (res.data.code === 0 || res.data.code === 2) {
    return res.data;
  }
  return Promise.reject(new Error(res.data.message));
}

/**
 * 获取当前用户信息
 */
export async function getPersonInfoData(params) {
  const res = await request.get('/personInfo', { params });
  if (res.data.code === 0) {
    return res.data.data;
  }
  return Promise.reject(new Error(res.data.message));
}

/**
 * 分页查询
 */
export async function queryPage(routeType, params) {
  const res = await request.get('/personInfo/' + routeType + '/queryPage', {
    params
  });
  if (res.data.code === 0) {
    return res.data.data;
  }
  return Promise.reject(new Error(res.data.message));
}

/**
 * 组信息查询
 */
export async function getGroupList(routeType) {
  const res = await request.get('/personInfo/' + routeType + '/fieldGroupList');
  if (res.data.code === 0 && res.data.data) {
    return res.data.data;
  }
  return Promise.reject(new Error(res.data.message));
}

/**
 * 字段信息查询
 */
export async function getFieldList(routeType, params) {
  const res = await request.get('/personInfo/' + routeType + '/fieldList', {
    params
  });
  if (res.data.code === 0 && res.data.data) {
    return res.data.data;
  }
  return Promise.reject(new Error(res.data.message));
}

/**
 * 根据学工号查询
 * /personInfo/{routeType}/{xgh}
 */
export async function getPersonInfo(routeType, xgh) {
  const res = await request.get(
    '/personInfo/' + routeType + (xgh ? '/' + xgh : '')
  );
  if (res.data.code === 0 && res.data.data) {
    return res.data.data;
  }
  return Promise.reject(new Error(res.data.message));
}

/**
 * 添加&修改
 */
export async function operation(routeType, data) {
  const res = await request.post(
    '/personInfo/' + routeType + '/operation',
    data
  );
  if (res.data.code === 0) {
    return res.data.message;
  }
  return Promise.reject(new Error(res.data.message));
}

/**
 * 批量删除
 */
export async function removes(data) {
  const res = await request.post('/code/codeType/remove', data);
  if (res.data.code === 0) {
    return res.data.message;
  }
  return Promise.reject(new Error(res.data.message));
}
