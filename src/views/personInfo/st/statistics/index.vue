<template>
  <ele-page>
    <statistics-card />
    <map-card />
    <sale-card />
    <el-row :gutter="8">
      <el-col :lg="12" :md="24" :sm="24" :xs="24">
        <browser-card />
      </el-col>
      <el-col :lg="12" :md="12" :sm="24" :xs="24">
        <visit-hour />
      </el-col>
    </el-row>
    <el-row :gutter="8">
      <el-col :lg="12" :md="24" :sm="24" :xs="24">
        <sale-card1 />
      </el-col>
      <el-col :lg="12" :md="12" :sm="24" :xs="24">
        <browser-card1 />
      </el-col>
    </el-row>
    <!--        <el-row :gutter="8">-->
    <!--            <el-col :lg="12" :md="24" :sm="24" :xs="24">-->
    <!--                <user-rate/>-->
    <!--            </el-col>-->
    <!--            <el-col :lg="12" :md="12" :sm="24" :xs="24">-->
    <!--                <user-satisfaction/>-->
    <!--            </el-col>-->
    <!--        </el-row>-->
  </ele-page>
</template>

<script setup>
  import StatisticsCard from './components/statistics-card.vue';
  import MapCard from './components/map-card.vue';
  import BrowserCard from './components/browser-card.vue';
  import BrowserCard1 from './components/browser-card1.vue';
  import SaleCard from './components/sale-card.vue';
  import SaleCard1 from './components/sale-card1.vue';
  import VisitHour from './components/visit-hour.vue';
</script>

<script>
  export default {
    name: 'DashboardMonitor'
  };
</script>
