<template>
  <ele-card header="各专业分布" :body-style="{ padding: '16px 6px 0 0' }">
    <template #extra>
      <el-link type="primary" :underline="false">更多</el-link>
    </template>
    <v-chart
      ref="browserChartRef"
      :option="browserChartOption"
      style="height: 362px"
    />
  </ele-card>
</template>

<script setup>
  import { reactive, ref } from 'vue';
  import { use } from 'echarts/core';
  import { CanvasRenderer } from 'echarts/renderers';
  import { PieChart } from 'echarts/charts';
  import { LegendComponent, TooltipComponent } from 'echarts/components';
  import VChart from 'vue-echarts';
  import { useEcharts } from '@/utils/use-echarts';

  use([Canvas<PERSON><PERSON><PERSON>, PieChart, TooltipComponent, LegendComponent]);

  const browserChartRef = ref(null);

  useEcharts([browserChartRef]);

  /** 浏览器分布饼图配置 */
  const browserChartOption = reactive({});

  /** 获取用户浏览器分布数据 */
  const getBrowserCountData = () => {
    let data = [
      {
        name: 'Chrome',
        value: 9052
      },
      {
        name: 'Safari',
        value: 535
      },
      {
        name: 'Firefox',
        value: 1610
      },
      {
        name: 'Edge',
        value: 2800
      },
      {
        name: 'IE',
        value: 3200
      },
      {
        name: 'Other',
        value: 1700
      }
    ];

    Object.assign(browserChartOption, {
      tooltip: {
        trigger: 'item',
        confine: true,
        borderWidth: 1
      },
      legend: {
        bottom: 5,
        itemWidth: 10,
        itemHeight: 10,
        icon: 'circle',
        data: data.map((d) => d.name)
      },
      series: [
        {
          type: 'pie',
          radius: ['45%', '70%'],
          center: ['50%', '43%'],
          label: {
            show: false
          },
          data: data
        }
      ]
    });
  };

  getBrowserCountData();
</script>
