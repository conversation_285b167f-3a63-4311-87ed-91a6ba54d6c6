<template>
  <ele-page flex-table>
    <!-- 搜索表单 -->
    <search @search="searchReload" />
    <ele-card
      flex-table
      :body-style="{ padding: '0 5px 10px 5px!important', overflow: 'hidden' }"
    >
      <!-- 表格 -->
      <ele-pro-table
        ref="tableRef"
        row-key="xgh"
        :columns="columns"
        :datasource="datasource"
        :border="true"
        :show-overflow-tooltip="true"
        v-model:selections="selections"
        highlight-current-row
        @done="onDone"
      >
        <template #toolbar>
          <el-button
            type="primary"
            size="small"
            class="ele-btn-icon"
            :icon="PlusOutlined"
            @click="openEdit()"
          >
            新建
          </el-button>
          <el-button
            type="danger"
            size="small"
            class="ele-btn-icon"
            :icon="DeleteOutlined"
            @click="remove()"
          >
            删除
          </el-button>
          <el-button
            class="ele-btn-icon"
            size="small"
            :icon="UploadOutlined"
            @click="openImport"
          >
            导入
          </el-button>
          <el-button size="small" :icon="DownloadOutlined" @click="exportBas()">
            导出
          </el-button>
          <el-button
            size="small"
            :icon="UploadOutlined"
            @click="openImportUpdate()"
          >
            批量更新
          </el-button>
          <el-button
            size="small"
            :icon="DownloadOutlined"
            @click="exportBasDiySet()"
          >
            自定义导出设置
          </el-button>
          <el-button
            size="small"
            :icon="UploadOutlined"
            @click="openImportFile()"
          >
            照片批量上传
          </el-button>
          <el-button
            size="small"
            :icon="DownloadOutlined"
            @click="exportFile()"
          >
            照片打包下载
          </el-button>
        </template>

        <template #action="{ row }">
          <!--                    <el-divider direction="vertical"/>-->
          <el-link type="primary" :underline="false" @click="openEdit(row)">
            修改
          </el-link>
          <el-divider direction="vertical" />
          <el-link type="primary" :underline="false" @click="remove(row)">
            删除
          </el-link>
        </template>
        <!-- 用户名表头 -->
        <template #groupNameHeader="{ column }">
          <div style="display: flex; align-items: center">
            <div style="flex: 1; overflow: hidden; text-overflow: ellipsis">
              {{ column.label }}
            </div>
            <name-filter @search="onNameFilter" />
          </div>
        </template>
      </ele-pro-table>
    </ele-card>
    <!-- 编辑弹窗 -->
    <!--        <edit v-model="showEdit" :data="current" @done="reload"/>-->
    <!-- 导入弹窗 -->
    <Import v-model="showImport" @done="reload" />
    <ImportUpdate v-model="showImportUpdate" @done="reload" />
    <ImportFile v-model="showImportFile" @done="reload" />

    <ExportSet v-model="showBasDiySet" @done="updateExportData" />
  </ele-page>
</template>

<script setup>
  import { reactive, ref, unref } from 'vue';
  import { ElMessageBox } from 'element-plus/es';
  import { EleMessage } from 'ele-admin-plus/es';
  import {
    DeleteOutlined,
    DownloadOutlined,
    PlusOutlined,
    UploadOutlined
  } from '@/components/icons';
  import nameFilter from './components/name-filter.vue';
  import Import from './components/import.vue';
  import ImportUpdate from './components/import-update.vue';
  import ImportFile from './components/import-file.vue';
  import ExportSet from './components/export-set.vue';
  import Search from './components/search.vue';
  import { getFieldList, queryPage, removes } from './api/index';
  import { useRouter } from 'vue-router';
  import { useDictData } from '@/utils/use-dict-data';
  import { useUserStore } from '@/store/modules/user';
  import { getToken } from '@/utils/token-util';
  import { getCurrentRole } from '@/utils/current-role-util';

  const BASE_URL = import.meta.env.BASE_URL;
  const accessToken = getToken();
  /** 当前用户角色*/
  const currentRoles = getCurrentRole() ? JSON.parse(getCurrentRole()) : {};

  const { currentRoute, push } = useRouter();
  const { params, path } = unref(currentRoute);
  const routeType = path.split('/')[3];

  console.log(params, path);

  useDictData('nj');
  useDictData('pycc');
  const userStore = useUserStore();
  /** 表格实例 */
  const tableRef = ref(null);
  const currentPageData = ref([]);

  /** 用户名筛选值 */
  const nameFilterValue = ref('');

  /** 表格搜索参数 */
  const lastWhere = reactive({});
  /** 普通查询 -查询参数*/
  const searchWhere = reactive({});

  /** 表格列配置 */
  const columns = ref([]);
  const initFieldList = async () => {
    let newColumn = [
      {
        type: 'selection',
        columnKey: 'selection',
        width: 50,
        align: 'center',
        fixed: 'left'
      }
    ];
    const data = await getFieldList(routeType, { listShowFlag: '是' });
    if (data.length > 0) {
      data.forEach((e) => {
        if (e.controlType === 'select' && e.loadDataType) {
          if (e.loadDataType === 'other') {
            newColumn.push({
              minWidth: 110,
              prop: e.fieldEn,
              label: e.fieldZh,
              formatter: (row) => {
                let finallyVal = '';
                switch (e.loadDataUrl) {
                  case '/code/codeBjb':
                    finallyVal = row['bjmc'];
                    break;
                  case '/code/codeZyb':
                    finallyVal = row['zymc'];
                    break;
                  case '/code/codeDwb':
                    finallyVal = row['xymc'];
                    break;
                  default:
                    finallyVal = row[e.fieldEn];
                    break;
                }
                return finallyVal;
              }
            });
          } else {
            newColumn.push({
              minWidth: 110,
              prop: e.fieldEn,
              label: e.fieldZh,
              formatter: (row) => {
                // if (e.valueField) {
                //   const {dicts} = storeToRefs(userStore);
                //   if (dicts.value[e.loadDataType]) {
                //     return
                //   } else {d
                //     useDictData(e.loadDataType, {});
                //   }
                //   let newArray = row[e.fieldEn] ? [row[e.fieldEn]] : []
                //   return transformDicDataName(dicts.value[e.loadDataType], newArray);
                // } else {
                return row[e.fieldEn];
                // }
              }
            });
          }
        } else {
          newColumn.push({
            minWidth: 110,
            prop: e.fieldEn,
            label: e.fieldZh
          });
        }
      });
    }
    newColumn.push({
      columnKey: 'action',
      label: '操作',
      slot: 'action',
      width: 100,
      fixed: 'right'
    });
    columns.value = newColumn;
  };

  /** 表格选中数据 */
  const selections = ref([]);

  /** 当前编辑数据 */
  const current = ref(null);

  /** 是否显示编辑弹窗 */
  const showEdit = ref(false);

  const currentLimit = ref(10);
  const currentPage = ref(1);

  /** 表格数据源 */
  const datasource = ({ page, limit, where, orders, filters }) => {
    currentLimit.value = limit;
    //默认排序
    // orders = Object.keys(orders).length > 0 ? orders : {
    //     sort: 'sortNumber',
    //     order: 'asc'
    // }
    return queryPage(routeType, {
      ...where,
      ...orders,
      ...filters,
      page,
      limit
    });
  };

  /** 用户名筛选事件 */
  const onNameFilter = (name) => {
    nameFilterValue.value = name;
    doReload();
  };

  const searchReload = (where) => {
    searchWhere.value = where;
    tableRef.value?.reload?.({ page: 1, where });
  };
  /** 表格搜索 */
  const doReload = () => {
    if (nameFilterValue.value) {
      reload({
        ...lastWhere,
        groupName: nameFilterValue.value
      });
    } else {
      reload(lastWhere);
    }
  };

  /** 搜索 */
  const reload = (where) => {
    selections.value = [];
    tableRef.value?.reload?.({ page: 1, where });
  };

  const onDone = (result) => {
    const { data, page } = result;
    currentPage.value = page;
    currentPageData.value = data;
  };

  /** 自定义导出，导出数据处理 */
  const updateExportData = (data) => {
    if (data) {
      let type = data?.type ?? '';
      let newStr = '';
      let dataKeys = Object.keys(data);
      dataKeys.forEach((key) => {
        if (data[key]) newStr += '&' + key + '=' + data[key];
      });
      if (type === '1') {
        newStr += '&page=' + currentPage.value + '&limit=' + currentLimit.value;
      } else if (type === '2') {
        const rows = selections.value.length > 0 ? selections.value : [];
        newStr += '&xgh=' + rows.map((d) => d.xgh);
      } else if (type === '3') {
        let obj = searchWhere.value;
        let keys = Object.keys(obj);
        keys.forEach((key) => {
          if (obj[key]) newStr += '&' + key + '=' + obj[key];
        });
      }
      window.location.href =
        BASE_URL +
        'api/personInfo/' +
        routeType +
        '/exportData?access_token=' +
        accessToken +
        '&roleId=' +
        currentRoles.roleId +
        newStr;
    }
  };

  /** 打开组group编辑弹窗 */
  const openEdit = (row) => {
    // let json = {
    //   xgh: row?.xgh,
    //   xm: row?.xm,
    //   routeType: routeType,
    // }
    // setSharedPersonData(json)
    if (row) {
      push('/personInfo/st/details/' + routeType + '/' + row?.xgh);
    } else {
      push('/personInfo/st/details/' + routeType + '/INSERT');
    }
  };

  /** 删除单个 */
  const remove = (row) => {
    const rows = row == null ? selections.value : [row];
    if (!rows.length) {
      EleMessage.error('请至少选择一条数据');
      return;
    }
    ElMessageBox.confirm(
      '确定要删除“' + rows.map((d) => d.groupName).join(', ') + '”吗?',
      '系统提示',
      { type: 'warning', draggable: true }
    )
      .then(() => {
        const loading = EleMessage.loading('请求中..');
        removes(rows.map((d) => d.id))
          .then((msg) => {
            loading.close();
            EleMessage.success(msg);
            reload();
          })
          .catch((e) => {
            loading.close();
            EleMessage.error(e.message);
          });
      })
      .catch(() => {});
  };

  /** 是否显示导入弹窗 */
  const showImport = ref(false);
  const showImportUpdate = ref(false);
  const showImportFile = ref(false);
  const showBasDiySet = ref(false);

  /** 打开导入弹窗 */
  const openImport = () => {
    showImport.value = true;
  };

  const openImportUpdate = () => {
    showImportUpdate.value = true;
  };

  /** 打开导入照片弹窗 */
  const openImportFile = () => {
    showImportFile.value = true;
  };

  /**  自定义导出设置  */
  const exportBasDiySet = () => {
    showBasDiySet.value = true;
  };

  /** 提交状态 */
  const loading = ref(false);

  /** 导出excel */
  const exportBas = () => {
    loading.value = true;
    setTimeout(() => {
      window.location.href =
        BASE_URL +
        'api/personInfo/' +
        routeType +
        '/exportData?access_token=' +
        accessToken +
        '&roleId=' +
        currentRoles.roleId;
      loading.value = false;
    }, 3500);
  };

  /** 照片打包下载 */
  const exportFile = () => {
    loading.value = true;
    setTimeout(() => {
      window.location.href =
        BASE_URL +
        'api/personInfo/' +
        routeType +
        '/downloadPhoto?access_token=' +
        accessToken +
        '&roleId=' +
        currentRoles.roleId;
      loading.value = false;
    }, 3500);
  };

  initFieldList();
</script>

<script>
  export default {
    name: 'PersonInfo'
  };
</script>

<style scoped>
  .el-link {
    font-weight: unset !important;
  }
</style>
