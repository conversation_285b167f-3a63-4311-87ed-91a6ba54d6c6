<template>
  <ele-drawer
    :size="530"
    :title="isUpdate ? '修改' : '新建'"
    :append-to-body="true"
    style="max-width: 100%"
    :destroy-on-close="true"
    :model-value="modelValue"
    :body-style="{ paddingBottom: '8px' }"
    @update:modelValue="updateModelValue"
  >
    <pro-form
      ref="formRef"
      :model="form"
      :items="items"
      :grid="{ span: 24 }"
      :labelWidth="110"
      @updateValue="setFieldValue"
    />
    <template #footer>
      <el-button @click="updateModelValue(false)">取消</el-button>
      <el-button type="primary" :loading="loading" @click="onSubmit">
        提交
      </el-button>
    </template>
  </ele-drawer>
</template>

<script setup>
  import { ref, unref, watch, computed } from 'vue';
  import { useFormData } from '@/utils/use-form-data';
  import { EleMessage } from 'ele-admin-plus';
  import { operation } from '../api';
  import ProForm from '@/components/ProForm/index.vue';
  import { useRouter } from 'vue-router';

  const BASE_URL = import.meta.env.BASE_URL;
  const emit = defineEmits(['done', 'update:modelValue']);

  const props = defineProps({
    /** 弹窗是否打开 */
    modelValue: Boolean,
    /** 修改回显的数据 */
    data: Object
  });

  const { currentRoute } = useRouter();
  const { params, path } = unref(currentRoute);
  const userType = path.split('/')[4];
  /** 是否是修改 */
  const isUpdate = ref(false);

  /** 表单实例 */
  const formRef = ref(null);
  /** 表单数据 */
  const [form, resetFields, assignFields, setFieldValue] = useFormData({
    id: void 0,
    name: '',
    bizId: '',
    sendChannel: '',
    accountConfig: '',
    status: '',
    // 渠道类型
    channelType: '',
    // 腾讯云短信配置
    tencentSupplierId: '10',
    tencentSupplierName: '腾讯云',
    tencentScriptName: 'TencentSmsScript',
    tencentUrl: 'sms.tencentcloudapi.com',
    tencentSecretId: '',
    tencentSecretKey: '',
    tencentSmsSdkAppId: '',
    tencentTemplateId: '',
    tencentSignName: '',
    // 阿里云短信配置
    aliyunSupplierId: '20',
    aliyunSupplierName: '阿里云',
    aliyunScriptName: 'AliyunSmsScript',
    aliyunUrl: 'dysmsapi.aliyuncs.com',
    aliyunRegion: 'cn-hangzhou',
    aliyunAccessKeyId: '',
    aliyunAccessKeySecret: '',
    aliyunTemplateCode: '',
    aliyunSignName: '',
    // 邮箱配置
    emailHost: 'smtp.exmail.qq.com',
    emailPort: 465,
    emailAuth: true,
    emailFrom: '',
    emailUser: '',
    emailPass: '',
    emailSslEnable: true,
    // 企业微信配置
    weworkCorpId: '',
    weworkCorpSecret: '',
    weworkAgentId: '',
    // 钉钉配置
    dingdingAppKey: '',
    dingdingAppSecret: '',
    dingdingAgentId: ''
  });

  /** 渠道类型选项 */
  const channelTypeOptions = [
    { value: 'tencent_sms', label: '腾讯云短信' },
    { value: 'aliyun_sms', label: '阿里云短信' },
    { value: 'email', label: '邮箱' },
    { value: 'wework', label: '企业微信' },
    { value: 'dingding', label: '钉钉' }
  ];

  /** 基础表单项 */
  const baseItems = [
    {
      prop: 'channelType',
      label: '渠道类型',
      type: 'select',
      options: channelTypeOptions,
      required: true,
      props: {
        placeholder: '请选择渠道类型'
      }
    },
    { prop: 'bizId', label: '业务方标识', type: 'input', required: true },
    { prop: 'sendChannel', label: '发送渠道', type: 'input' },
    {
      prop: 'status',
      label: '状态',
      type: 'dictSelect',
      props: {
        code: 'zt',
        dicQueryParams: {
          getValType: 'name'
        }
      },
      required: true
    }
  ];

  /** 腾讯云短信配置项 */
  const tencentSmsItems = [
    {
      prop: 'tencentSecretId',
      label: 'SecretId',
      type: 'input',
      required: true
    },
    {
      prop: 'tencentSecretKey',
      label: 'SecretKey',
      type: 'input',
      required: true
    },
    {
      prop: 'tencentSmsSdkAppId',
      label: 'SmsSdkAppId',
      type: 'input',
      required: true
    },
    {
      prop: 'tencentTemplateId',
      label: 'TemplateId',
      type: 'input',
      required: true
    },
    {
      prop: 'tencentSignName',
      label: 'SignName',
      type: 'input',
      required: true
    }
  ];

  /** 阿里云短信配置项 */
  const aliyunSmsItems = [
    {
      prop: 'aliyunAccessKeyId',
      label: 'AccessKeyId',
      type: 'input',
      required: true
    },
    {
      prop: 'aliyunAccessKeySecret',
      label: 'AccessKeySecret',
      type: 'input',
      required: true
    },
    {
      prop: 'aliyunTemplateCode',
      label: 'TemplateCode',
      type: 'input',
      required: true
    },
    { prop: 'aliyunSignName', label: 'SignName', type: 'input', required: true }
  ];

  /** 邮箱配置项 */
  const emailItems = [
    { prop: 'emailHost', label: 'Host', type: 'input', required: true },
    { prop: 'emailPort', label: 'Port', type: 'inputNumber', required: true },
    { prop: 'emailAuth', label: 'Auth', type: 'switch', required: true },
    { prop: 'emailFrom', label: 'From', type: 'input', required: true },
    { prop: 'emailUser', label: 'User', type: 'input', required: true },
    { prop: 'emailPass', label: 'Pass', type: 'input', required: true },
    {
      prop: 'emailSslEnable',
      label: 'SslEnable',
      type: 'switch',
      required: true
    }
  ];

  /** 企业微信配置项 */
  const weworkItems = [
    { prop: 'weworkCorpId', label: 'CorpId', type: 'input', required: true },
    {
      prop: 'weworkCorpSecret',
      label: 'CorpSecret',
      type: 'input',
      required: true
    },
    { prop: 'weworkAgentId', label: 'AgentId', type: 'input', required: true }
  ];

  /** 钉钉配置项 */
  const dingdingItems = [
    { prop: 'dingdingAppKey', label: 'AppKey', type: 'input', required: true },
    {
      prop: 'dingdingAppSecret',
      label: 'AppSecret',
      type: 'input',
      required: true
    },
    { prop: 'dingdingAgentId', label: 'AgentId', type: 'input', required: true }
  ];

  /** 动态表单项 */
  const items = computed(() => {
    const dynamicItems = [...baseItems];

    // 根据渠道类型添加对应的配置项
    if (form.channelType === 'tencent_sms') {
      dynamicItems.push(...tencentSmsItems);
    } else if (form.channelType === 'aliyun_sms') {
      dynamicItems.push(...aliyunSmsItems);
    } else if (form.channelType === 'email') {
      dynamicItems.push(...emailItems);
    } else if (form.channelType === 'wework') {
      dynamicItems.push(...weworkItems);
    } else if (form.channelType === 'dingding') {
      dynamicItems.push(...dingdingItems);
    }

    return dynamicItems;
  });

  /** 生成账号配置 */
  const generateAccountConfig = () => {
    let config = {};

    if (form.channelType === 'tencent_sms') {
      config = {
        supplierId: form.tencentSupplierId,
        supplierName: form.tencentSupplierName,
        scriptName: form.tencentScriptName,
        url: form.tencentUrl,
        secretId: form.tencentSecretId,
        secretKey: form.tencentSecretKey,
        smsSdkAppId: form.tencentSmsSdkAppId,
        templateId: form.tencentTemplateId,
        signName: form.tencentSignName
      };
    } else if (form.channelType === 'aliyun_sms') {
      config = {
        supplierId: form.aliyunSupplierId,
        supplierName: form.aliyunSupplierName,
        scriptName: form.aliyunScriptName,
        url: form.aliyunUrl,
        region: form.aliyunRegion,
        accessKeyId: form.aliyunAccessKeyId,
        accessKeySecret: form.aliyunAccessKeySecret,
        templateCode: form.aliyunTemplateCode,
        signName: form.aliyunSignName
      };
    } else if (form.channelType === 'email') {
      config = {
        host: form.emailHost,
        port: form.emailPort,
        auth: form.emailAuth,
        from: form.emailFrom,
        user: form.emailUser,
        pass: form.emailPass,
        sslEnable: form.emailSslEnable
      };
    } else if (form.channelType === 'wework') {
      config = {
        corpId: form.weworkCorpId,
        corpSecret: form.weworkCorpSecret,
        agentId: form.weworkAgentId
      };
    } else if (form.channelType === 'dingding') {
      config = {
        appKey: form.dingdingAppKey,
        appSecret: form.dingdingAppSecret,
        agentId: form.dingdingAgentId
      };
    }

    return JSON.stringify(config);
  };

  /** 提交状态 */
  const loading = ref(false);
  /** 提交 */
  const onSubmit = () => {
    formRef.value?.validate?.((valid) => {
      if (!valid) {
        return;
      }

      // 生成账号配置
      const accountConfig = generateAccountConfig();

      // 获取渠道名称
      const channelTypeOption = channelTypeOptions.find(
        (option) => option.value === form.channelType
      );
      const name = channelTypeOption ? channelTypeOption.label : '';

      // 准备提交数据
      const submitData = {
        id: form.id,
        name: name,
        bizId: form.bizId,
        sendChannel: form.sendChannel,
        accountConfig: accountConfig,
        status: form.status
      };

      loading.value = true;
      operation(submitData)
        .then((msg) => {
          loading.value = false;
          EleMessage.success(msg);
          updateModelValue(false);
          emit('done');
        })
        .catch((e) => {
          loading.value = false;
          EleMessage.error(e.message);
        });
    });
  };

  /** 根据渠道名称推断渠道类型 */
  const inferChannelTypeFromName = (name) => {
    if (!name) return '';

    const nameStr = String(name).toLowerCase();
    if (nameStr.includes('腾讯') || nameStr.includes('tencent')) {
      return 'tencent_sms';
    } else if (nameStr.includes('阿里') || nameStr.includes('aliyun')) {
      return 'aliyun_sms';
    } else if (
      nameStr.includes('邮箱') ||
      nameStr.includes('email') ||
      nameStr.includes('mail')
    ) {
      return 'email';
    } else if (
      nameStr.includes('企业微信') ||
      nameStr.includes('微信') ||
      nameStr.includes('wework')
    ) {
      return 'wework';
    } else if (nameStr.includes('钉钉') || nameStr.includes('dingding')) {
      return 'dingding';
    }
    return '';
  };

  /** 解析账号配置 */
  const parseAccountConfig = (data) => {
    if (!data) {
      return {};
    }

    let parsedData = {};

    const inferredType = inferChannelTypeFromName(data.name);
    if (inferredType) {
      parsedData.channelType = inferredType;
    }

    // 如果有账号配置，则解析配置内容
    if (data.accountConfig) {
      try {
        const config = JSON.parse(data.accountConfig);
        if (
          String(config.supplierId) === '10' ||
          config.scriptName === 'TencentSmsScript' ||
          config.url === 'sms.tencentcloudapi.com' ||
          config.hasOwnProperty('secretId') ||
          config.hasOwnProperty('smsSdkAppId')
        ) {
          parsedData.channelType = 'tencent_sms';
          parsedData.tencentSecretId = config.secretId || '';
          parsedData.tencentSecretKey = config.secretKey || '';
          parsedData.tencentSmsSdkAppId = config.smsSdkAppId || '';
          parsedData.tencentTemplateId = config.templateId || '';
          parsedData.tencentSignName = config.signName || '';
        } else if (
          String(config.supplierId) === '20' ||
          config.scriptName === 'AliyunSmsScript' ||
          config.url === 'dysmsapi.aliyuncs.com' ||
          config.accessKeyId ||
          config.templateCode
        ) {
          parsedData.channelType = 'aliyun_sms';
          parsedData.aliyunAccessKeyId = config.accessKeyId || '';
          parsedData.aliyunAccessKeySecret = config.accessKeySecret || '';
          parsedData.aliyunTemplateCode = config.templateCode || '';
          parsedData.aliyunSignName = config.signName || '';
        } else if (config.host || config.port || config.from || config.user) {
          parsedData.channelType = 'email';
          parsedData.emailHost = config.host || 'smtp.exmail.qq.com';
          parsedData.emailPort = config.port || 465;
          parsedData.emailAuth = config.auth !== undefined ? config.auth : true;
          parsedData.emailFrom = config.from || '';
          parsedData.emailUser = config.user || '';
          parsedData.emailPass = config.pass || '';
          parsedData.emailSslEnable =
            config.sslEnable !== undefined ? config.sslEnable : true;
        } else if (config.corpId || config.corpSecret) {
          parsedData.channelType = 'wework';
          parsedData.weworkCorpId = config.corpId || '';
          parsedData.weworkCorpSecret = config.corpSecret || '';
          parsedData.weworkAgentId = config.agentId || '';
        } else if (config.appKey || config.appSecret) {
          parsedData.channelType = 'dingding';
          parsedData.dingdingAppKey = config.appKey || '';
          parsedData.dingdingAppSecret = config.appSecret || '';
          parsedData.dingdingAgentId = config.agentId || '';
        }
      } catch (e) {
        console.error('解析账号配置失败:', e);
      }
    }

    console.log('最终解析结果:', parsedData);
    return parsedData;
  };

  /** 更新modelValue */
  const updateModelValue = (value) => {
    emit('update:modelValue', value);
  };

  // 监听弹窗状态变化
  watch(
    () => props.modelValue,
    (modelValue) => {
      if (modelValue) {
        if (props.data) {
          const parsedConfig = parseAccountConfig(props.data);
          const finalData = {
            ...props.data,
            ...parsedConfig
          };
          assignFields(finalData);
          isUpdate.value = true;
        } else {
          resetFields();
          formRef.value?.clearValidate?.();
          isUpdate.value = false;
        }
      } else {
        resetFields();
        formRef.value?.clearValidate?.();
      }
    },
    { immediate: true }
  );

  // 监听数据变化（处理数据延迟传入的情况）
  watch(
    () => props.data,
    (newData) => {
      if (props.modelValue && newData) {
        const parsedConfig = parseAccountConfig(newData);
        const finalData = {
          ...newData,
          ...parsedConfig
        };
        assignFields(finalData);
        isUpdate.value = true;
      }
    },
    { immediate: true, deep: true }
  );
</script>
