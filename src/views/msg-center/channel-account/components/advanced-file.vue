<template>
  <div>
    <div style="padding: 8px 16px">
      <el-upload
        action=""
        :show-upload-list="false"
        :before-upload="onUpload"
        style="display: inline-block; vertical-align: middle"
      >
        <el-button type="primary" class="ele-btn-icon" :icon="UploadOutlined">
          上传
        </el-button>
      </el-upload>
    </div>
    {{ datas }}
    <div class="demo-file-list">
      <ele-file-list
        :data="datas"
        :grid="grid"
        :sort="sort"
        :sortable="true"
        :box-choose="true"
        :context-menus="contextMenus"
        :context-menu-props="{
          menuStyle: { minWidth: '120px' },
          iconProps: { size: 15 }
        }"
        :icons="localIcons"
        :small-icons="localSmallIcons"
        :style="{ minHeight: '400px', minWidth: grid ? 'auto' : '456px' }"
        @item-click="onItemClick"
        @item-context-menu="onCtxMenuClick"
      >
        <!-- 例如自定义图标加点自己的样式，以及在左上角加个小红点 -->
        <template #icon="{ item, icon }">
          <img :src="icon" style="border-radius: 50%" />
          <div
            style="
              width: 8px;
              height: 8px;
              background: red;
              border-radius: 50%;
              position: absolute;
              top: 6px;
              left: 6px;
            "
          ></div>
        </template>
      </ele-file-list>
    </div>
    <!-- 用于图片预览 -->
    <div style="display: none">
      <el-image
        ref="previewRef"
        v-if="previewOption.visible"
        :src="previewOption.currentSrc"
        :preview-src-list="previewImages"
        :initial-index="previewOption.current"
        :hide-on-click-modal="true"
        :preview-teleported="true"
      />
    </div>
  </div>
</template>

<script setup>
  import {
    localIcons,
    localSmallIcons
  } from 'ele-admin-plus/es/ele-file-list/icons';
  import { computed, nextTick, reactive, ref } from 'vue';
  import { ElMessageBox } from 'element-plus/es';
  import { EleMessage } from 'ele-admin-plus/es';
  import { removeUserFile } from '@/api/system/user-file';
  import {
    DeleteOutlined,
    DownloadOutlined,
    DragOutlined,
    EditOutlined
  } from '@/components/icons';

  const emit = defineEmits(['update:modelValue']);

  const props = defineProps({
    /** 数据 */
    modelValue: Array,
    limit: Number
  });
  /** 图片预览列表 */
  const previewImages = ref([]);

  /** 图片预览配置 */
  const previewOption = reactive({
    currentSrc: '',
    current: 0,
    visible: false
  });
  /** 图片预览组件 */
  const previewRef = ref(null);

  /** 是否网格展示 */
  const grid = ref(true);

  /** 数据 */
  const datas = computed(() => {
    const result = [];
    const val = props.modelValue;
    if (val == null || val === '') {
      return result;
    }
    const values = Array.isArray(val) ? val : [val];
    return values;
  });

  /** 是否禁用 */
  const disabled = ref(false);

  /** 上传状态 */
  const loading = ref(false);

  /** 上传事件, 只添加, 不请求后台 */
  const onUpload = (item) => {
    if (!item.file) {
      return;
    }
    if (file.size / 1024 / 1024 > 100) {
      EleMessage.error('大小不能超过 100MB');
      return false;
    }
    datas.value.push({ ...item });
    updateModelValue();
  };

  /** 更新modelValue */
  const updateModelValue = () => {
    emit('update:modelValue', datas.value);
  };

  /** 删除事件 */
  const onRemove = (item) => {
    ElMessageBox.confirm('确定要删除吗?', '系统提示', {
      type: 'warning',
      draggable: true
    })
      .then(() => {
        datas.value.splice(datas.value.indexOf(item), 1);
      })
      .catch(() => {});
  };

  /** 判断是否是图片文件 */
  const isImageFile = (item) => {
    return (
      typeof item.contentType === 'string' &&
      item.contentType.startsWith('image/') &&
      item.url
    );
  };

  /** 预览图片文件 */
  const previewItemImage = (item) => {
    const temp = props.modelValue.filter((d) => isImageFile(d));
    const index = temp.indexOf(item);
    if (index !== -1) {
      previewImages.value = temp.map((d) => d.url);
      previewOption.currentSrc = item.url;
      previewOption.current = index;
      previewOption.visible = true;
      nextTick(() => {
        previewRef.value?.$el?.querySelector?.('img')?.click?.();
      });
    }
  };

  /** item 点击事件 */
  const onItemClick = (item) => {
    if (item.isImageFile) {
      // 预览图片文件
      previewItemImage(item);
    } else {
      //下载文件
      window.open(item.url);
    }
  };
  /** 右键菜单点击事件 */
  const onCtxMenuClick = (option) => {
    const { key, item } = option;
    if (key === 'open') {
      // 打开文件
      if (item.isDirectory || isImageFile(item)) {
        onItemClick(item);
      } else {
        window.open(item.url);
      }
    } else if (key === 'download') {
      // 下载文件
      if (typeof item.downloadUrl === 'string') {
        window.open(item.downloadUrl);
      }
    } else if (key === 'edit') {
      // 重命名
      nameEditData.value = item;
      nameEditVisible.value = true;
    } else if (key === 'remove') {
      // 删除文件
      removeItem(item);
    } else if (key === 'move') {
      // 移动
      EleMessage.info({ message: `点击了移动到: ${item.name}`, plain: true });
    }
  };

  /** 右键菜单 */
  const contextMenus = (item) => {
    if (!item) {
      return [];
    }
    const menus = [
      {
        title: '重命名',
        command: 'edit',
        icon: EditOutlined,
        divided: item.isDirectory
      },
      {
        title: '移动到',
        command: 'move',
        icon: DragOutlined
      },
      {
        title: '删除',
        command: 'remove',
        icon: DeleteOutlined,
        divided: true,
        danger: true
      }
    ];
    if (!item.isDirectory) {
      menus.unshift({
        title: '下载',
        command: 'download',
        icon: DownloadOutlined,
        divided: true
      });
    }
    menus.unshift({ title: '打开', command: 'open' });
    return menus;
  };
  /** 删除 */
  const removeItem = (item) => {
    ElMessageBox.confirm(`确定要删除“${item.name}”吗?`, '系统提示', {
      type: 'warning',
      draggable: true
    })
      .then(() => {
        const loading = EleMessage.loading('请求中..');
        removeUserFile(item.id)
          .then((msg) => {
            loading.close();
            EleMessage.success(msg);
            onDone();
          })
          .catch((e) => {
            loading.close();
            EleMessage.error(e.message);
          });
      })
      .catch(() => {});
  };
</script>

<style lang="scss" scoped>
  .demo-file-list {
    position: relative;
    overflow-x: auto;

    .demo-file-list-empty {
      position: absolute;
      top: 100px;
      left: 50%;
      transform: translateX(-50%);
    }
  }
</style>
