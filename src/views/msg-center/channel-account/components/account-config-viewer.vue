<template>
  <div class="account-config-viewer">
    <!-- 腾讯云短信配置 -->
    <div v-if="channelType === 'tencent_sms'" class="config-content">
      <div class="config-header">
        <el-tag type="primary" size="small">
          <el-icon><Message /></el-icon>
          腾讯云短信
        </el-tag>
      </div>
      <div class="config-items">
        <div class="config-item" v-if="parsedConfig.secretId">
          <span class="label">SecretId:</span>
          <span class="value" :title="parsedConfig.secretId">{{
            maskSensitiveInfo(parsedConfig.secretId)
          }}</span>
        </div>
        <div class="config-item" v-if="parsedConfig.smsSdkAppId">
          <span class="label">AppId:</span>
          <span class="value" :title="parsedConfig.smsSdkAppId">{{
            parsedConfig.smsSdkAppId
          }}</span>
        </div>
        <div class="config-item" v-if="parsedConfig.templateId">
          <span class="label">模板ID:</span>
          <span class="value" :title="parsedConfig.templateId">{{
            parsedConfig.templateId
          }}</span>
        </div>
        <div class="config-item" v-if="parsedConfig.signName">
          <span class="label">签名:</span>
          <span class="value" :title="parsedConfig.signName">{{
            parsedConfig.signName
          }}</span>
        </div>
      </div>
    </div>

    <!-- 阿里云短信配置 -->
    <div v-else-if="channelType === 'aliyun_sms'" class="config-content">
      <div class="config-header">
        <el-tag type="warning" size="small">
          <el-icon><Message /></el-icon>
          阿里云短信
        </el-tag>
      </div>
      <div class="config-items">
        <div class="config-item" v-if="parsedConfig.accessKeyId">
          <span class="label">AccessKeyId:</span>
          <span class="value" :title="parsedConfig.accessKeyId">{{
            maskSensitiveInfo(parsedConfig.accessKeyId)
          }}</span>
        </div>
        <div class="config-item" v-if="parsedConfig.templateCode">
          <span class="label">模板代码:</span>
          <span class="value" :title="parsedConfig.templateCode">{{
            parsedConfig.templateCode
          }}</span>
        </div>
        <div class="config-item" v-if="parsedConfig.signName">
          <span class="label">签名:</span>
          <span class="value" :title="parsedConfig.signName">{{
            parsedConfig.signName
          }}</span>
        </div>
        <div class="config-item" v-if="parsedConfig.region">
          <span class="label">区域:</span>
          <span class="value" :title="parsedConfig.region">{{
            parsedConfig.region
          }}</span>
        </div>
      </div>
    </div>

    <!-- 邮箱配置 -->
    <div v-else-if="channelType === 'email'" class="config-content">
      <div class="config-header">
        <el-tag type="success" size="small">
          <el-icon><Message /></el-icon>
          邮箱服务
        </el-tag>
      </div>
      <div class="config-items">
        <div class="config-item" v-if="parsedConfig.host">
          <span class="label">SMTP服务器:</span>
          <span class="value" :title="parsedConfig.host">{{
            parsedConfig.host
          }}</span>
        </div>
        <div class="config-item" v-if="parsedConfig.port">
          <span class="label">端口:</span>
          <span class="value" :title="String(parsedConfig.port)">{{
            parsedConfig.port
          }}</span>
        </div>
        <div class="config-item" v-if="parsedConfig.from">
          <span class="label">发件人:</span>
          <span class="value" :title="parsedConfig.from">{{
            parsedConfig.from
          }}</span>
        </div>
        <div class="config-item" v-if="parsedConfig.user">
          <span class="label">用户名:</span>
          <span class="value" :title="parsedConfig.user">{{
            maskSensitiveInfo(parsedConfig.user)
          }}</span>
        </div>
        <div class="config-item">
          <span class="label">SSL:</span>
          <el-tag
            :type="parsedConfig.sslEnable ? 'success' : 'info'"
            size="small"
          >
            {{ parsedConfig.sslEnable ? '启用' : '禁用' }}
          </el-tag>
        </div>
      </div>
    </div>

    <!-- 企业微信配置 -->
    <div v-else-if="channelType === 'wework'" class="config-content">
      <div class="config-header">
        <el-tag type="success" size="small">
          <el-icon><ChatDotRound /></el-icon>
          企业微信
        </el-tag>
      </div>
      <div class="config-items">
        <div class="config-item" v-if="parsedConfig.corpId">
          <span class="label">CorpId:</span>
          <span class="value" :title="parsedConfig.corpId">{{
            maskSensitiveInfo(parsedConfig.corpId)
          }}</span>
        </div>
        <div class="config-item" v-if="parsedConfig.agentId">
          <span class="label">AgentId:</span>
          <span class="value" :title="parsedConfig.agentId">{{
            parsedConfig.agentId
          }}</span>
        </div>
      </div>
    </div>

    <!-- 钉钉配置 -->
    <div v-else-if="channelType === 'dingding'" class="config-content">
      <div class="config-header">
        <el-tag type="warning" size="small">
          <el-icon><ChatDotRound /></el-icon>
          钉钉
        </el-tag>
      </div>
      <div class="config-items">
        <div class="config-item" v-if="parsedConfig.appKey">
          <span class="label">AppKey:</span>
          <span class="value" :title="parsedConfig.appKey">{{
            maskSensitiveInfo(parsedConfig.appKey)
          }}</span>
        </div>
        <div class="config-item" v-if="parsedConfig.agentId">
          <span class="label">AgentId:</span>
          <span class="value" :title="parsedConfig.agentId">{{
            parsedConfig.agentId
          }}</span>
        </div>
      </div>
    </div>

    <!-- 未知类型或解析失败 -->
    <div v-else class="config-content">
      <div class="config-header">
        <el-tag type="info" size="small">
          <el-icon><Document /></el-icon>
          配置信息
        </el-tag>
      </div>
      <div class="config-raw">
        <el-text type="info" size="small">
          {{ config || '暂无配置' }}
        </el-text>
      </div>
    </div>

    <!-- 查看详情按钮 -->
    <div
      class="config-actions"
      v-if="config && parsedConfig && Object.keys(parsedConfig).length > 0"
    >
      <el-button
        type="primary"
        link
        size="small"
        @click="showDetail = !showDetail"
      >
        <el-icon><Document /></el-icon>
        {{ showDetail ? '收起' : '详情' }}
      </el-button>
    </div>

    <!-- 详情展开 -->
    <el-collapse-transition>
      <div v-show="showDetail" class="config-detail">
        <pre class="config-json">{{ formattedConfig }}</pre>
      </div>
    </el-collapse-transition>
  </div>
</template>

<script setup>
  import { ref, computed } from 'vue';
  import { Message, ChatDotRound, Document } from '@element-plus/icons-vue';

  const props = defineProps({
    config: {
      type: String,
      default: ''
    },
    channelName: {
      type: String,
      default: ''
    }
  });

  const showDetail = ref(false);

  /** 解析配置 */
  const parsedConfig = computed(() => {
    if (!props.config) return {};

    try {
      return JSON.parse(props.config);
    } catch (e) {
      return {};
    }
  });

  /** 格式化配置用于显示 */
  const formattedConfig = computed(() => {
    if (!props.config) return '';

    try {
      const config = JSON.parse(props.config);
      return JSON.stringify(config, null, 2);
    } catch (e) {
      return props.config;
    }
  });

  /** 推断渠道类型 */
  const channelType = computed(() => {
    const config = parsedConfig.value;
    const name = props.channelName?.toLowerCase() || '';

    // 从配置推断
    if (
      String(config.supplierId) === '10' ||
      config.scriptName === 'TencentSmsScript' ||
      config.secretId ||
      config.smsSdkAppId
    ) {
      return 'tencent_sms';
    } else if (
      String(config.supplierId) === '20' ||
      config.scriptName === 'AliyunSmsScript' ||
      config.accessKeyId
    ) {
      return 'aliyun_sms';
    } else if (config.host || config.port || config.from) {
      return 'email';
    } else if (config.corpId || config.corpSecret) {
      return 'wework';
    } else if (config.appKey || config.appSecret) {
      return 'dingding';
    }

    // 从名称推断
    if (name.includes('腾讯') || name.includes('tencent')) {
      return 'tencent_sms';
    } else if (name.includes('阿里') || name.includes('aliyun')) {
      return 'aliyun_sms';
    } else if (name.includes('邮箱') || name.includes('email')) {
      return 'email';
    } else if (name.includes('企业微信') || name.includes('微信')) {
      return 'wework';
    } else if (name.includes('钉钉')) {
      return 'dingding';
    }

    return 'unknown';
  });

  /** 脱敏处理敏感信息 */
  const maskSensitiveInfo = (value) => {
    if (!value) return '';
    const str = String(value);
    if (str.length <= 8) {
      return str.substring(0, 3) + '***';
    }
    return str.substring(0, 6) + '***' + str.substring(str.length - 3);
  };
</script>

<style scoped>
  .account-config-viewer {
    font-size: 12px;
    max-width: 100%;
  }

  .config-content {
    margin-bottom: 6px;
  }

  .config-header {
    margin-bottom: 6px;
  }

  .config-items {
    display: flex;
    flex-direction: column;
    gap: 3px;
  }

  .config-item {
    display: flex;
    align-items: center;
    gap: 6px;
    min-height: 20px;
  }

  .config-item .label {
    color: #666;
    font-weight: 500;
    min-width: 70px;
    font-size: 11px;
    flex-shrink: 0;
  }

  .config-item .value {
    color: #333;
    font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
    font-size: 11px;
    flex: 1;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }

  .config-raw {
    padding: 8px;
    background: #f5f5f5;
    border-radius: 4px;
    font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
    font-size: 11px;
    word-break: break-all;
  }

  .config-actions {
    margin-top: 6px;
    text-align: right;
  }

  .config-actions .el-button {
    padding: 2px 6px;
    font-size: 11px;
    height: auto;
  }

  .config-detail {
    margin-top: 6px;
    padding: 8px;
    background: #f8f9fa;
    border-radius: 4px;
    border: 1px solid #e9ecef;
    max-height: 200px;
    overflow-y: auto;
  }

  .config-json {
    margin: 0;
    font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
    font-size: 10px;
    color: #333;
    white-space: pre-wrap;
    word-break: break-all;
    line-height: 1.3;
  }
</style>
