<template>
  <ele-page flex-table>
    <search @search="reload" />
    <ele-card
      flex-table
      :body-style="{ padding: '0 5px 10px 5px!important', overflow: 'hidden' }"
    >
      <!-- 表格 -->
      <ele-pro-table
        ref="tableRef"
        row-key="id"
        :columns="columns"
        :datasource="datasource"
        :border="true"
        :show-overflow-tooltip="true"
        v-model:selections="selections"
        highlight-current-row
        cache-key="sphfwDictionaryGroup"
      >
        <template #toolbar>
          <el-button
            type="primary"
            size="small"
            class="ele-btn-icon"
            :icon="PlusOutlined"
            @click="openEdit()"
          >
            新建
          </el-button>
          <el-button
            type="danger"
            size="small"
            class="ele-btn-icon"
            :icon="DeleteOutlined"
            @click="remove()"
          >
            删除
          </el-button>
        </template>
        <!-- 账号配置美化显示 -->
        <template #accountConfig="{ row }">
          <div class="account-config-display">
            <account-config-viewer
              :config="row.accountConfig"
              :channel-name="row.name"
            />
          </div>
        </template>
        <template #action="{ row }">
          <el-link type="primary" :underline="false" @click="openEdit(row)">
            修改
          </el-link>
          <el-divider direction="vertical" />
          <el-link type="primary" :underline="false" @click="remove(row)">
            删除
          </el-link>
        </template>
        <!-- 用户名表头 -->
        <template #groupNameHeader="{ column }">
          <div style="display: flex; align-items: center">
            <div style="flex: 1; overflow: hidden; text-overflow: ellipsis">
              {{ column.label }}
            </div>
            <name-filter @search="onNameFilter" />
          </div>
        </template>
      </ele-pro-table>
    </ele-card>
    <!-- 编辑弹窗 -->
    <edit v-model="showEdit" :data="current" @done="reload" />
  </ele-page>
</template>

<script setup>
  import { reactive, ref, unref } from 'vue';
  import { ElMessageBox } from 'element-plus/es';
  import { EleMessage } from 'ele-admin-plus/es';
  import { DeleteOutlined, PlusOutlined } from '@/components/icons';
  import Edit from './components/edit.vue';
  import nameFilter from './components/name-filter.vue';
  import AccountConfigViewer from './components/account-config-viewer.vue';
  import { queryPage, removes } from './api';
  import { useRouter } from 'vue-router';
  import Search from './components/search.vue';

  const { currentRoute, push } = useRouter();
  const { params, path } = unref(currentRoute);
  const userType = path.split('/')[4];

  /** 表格实例 */
  const tableRef = ref(null);

  /** 表格列配置 */
  const columns = ref([
    {
      type: 'selection',
      columnKey: 'selection',
      width: 50,
      align: 'center',
      fixed: 'left'
    },
    {
      prop: 'name',
      label: '渠道名称',
      width: 150
    },
    {
      prop: 'bizId',
      label: '业务方标识',
      width: 150
    },
    {
      prop: 'sendChannel',
      label: '发送渠道',
      width: 100
    },
    {
      prop: 'accountConfig',
      label: '账号配置',
      slot: 'accountConfig',
      minWidth: 300
    },
    {
      prop: 'status',
      label: '状态',
      width: 100
    },
    {
      columnKey: 'action',
      label: '操作',
      slot: 'action',
      width: 140,
      fixed: 'right'
    }
  ]);

  /** 表格选中数据 */
  const selections = ref([]);

  /** 当前编辑数据 */
  const current = ref(null);

  /** 是否显示编辑弹窗 */
  const showEdit = ref(false);

  /** 表格数据源 */
  const datasource = ({ page, limit, where, orders, filters }) => {
    return queryPage({ ...where, ...orders, ...filters, page, limit });
  };
  /** 用户名筛选值 */
  const nameFilterValue = ref('');

  /** 表格搜索参数 */
  const lastWhere = reactive({});

  /** 用户名筛选事件 */
  const onNameFilter = (name) => {
    nameFilterValue.value = name;
    doReload();
  };
  /** 表格搜索 */
  const doReload = () => {
    if (nameFilterValue.value) {
      reload({
        ...lastWhere,
        groupName: nameFilterValue.value
      });
    } else {
      reload(lastWhere);
    }
  };
  /** 搜索 */
  const reload = (where) => {
    selections.value = [];
    tableRef.value?.reload?.({ page: 1, where });
  };

  /** 打开组group编辑弹窗 */
  const openEdit = (row) => {
    current.value = row ?? null;
    showEdit.value = true;
  };

  /** 删除单个 */
  const remove = (row) => {
    const rows = row == null ? selections.value : [row];
    if (!rows.length) {
      EleMessage.error('请至少选择一条数据');
      return;
    }
    ElMessageBox.confirm(
      '确定要删除“' + rows.map((d) => d.name).join(', ') + '”吗?',
      '系统提示',
      { type: 'warning', draggable: true }
    )
      .then(() => {
        const loading = EleMessage.loading('请求中..');
        removes(rows.map((d) => d.id))
          .then((msg) => {
            loading.close();
            EleMessage.success(msg);
            reload();
          })
          .catch((e) => {
            loading.close();
            EleMessage.error(e.message);
          });
      })
      .catch(() => {});
  };
</script>

<script>
  export default {
    name: 'MsgCenterChannelAccount'
  };
</script>

<style scoped>
  .el-link {
    font-weight: unset !important;
  }

  .account-config-display {
    max-width: 100%;
    overflow: hidden;
  }

  /* 表格行高度调整 */
  :deep(.el-table .el-table__row) {
    height: auto;
  }

  :deep(.el-table .el-table__cell) {
    padding: 12px 0;
  }

  /* 账号配置列样式优化 */
  :deep(.el-table .account-config-display) {
    line-height: 1.4;
  }
</style>
