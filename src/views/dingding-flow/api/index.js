import request from '@/utils/request';

/**
 * 节点状态设置
 */
export async function listStatusInit() {
  const data = [
    {
      key: '1',
      fieldEn: 'result',
      fieldZh: '审核状态',
      isEdit: true
    },
    {
      key: '2',
      fieldEn: 'statePass',
      fieldZh: '通过',
      isEdit: true
    },
    {
      key: '3',
      fieldEn: 'stateTerminate',
      fieldZh: '不通过',
      isEdit: true
    },
    {
      key: '4',
      fieldEn: 'stateReject',
      fieldZh: ' 驳回',
      isEdit: true
    }
  ];
  return data;
}

/**
 * 获取审核设置扩展字段列表
 */
export async function getFormExtendsField(params) {
  const res = await request.get('/workflow/formExtendsField', { params });
  if (res.data.code === 0) {
    return res.data.data;
  }
  return Promise.reject(new Error(res.data.message));
}

/**
 * 查询不分页
 */
export async function getWorkFlow(params) {
  const res = await request.get('/workflow', { params });
  if (res.data.code === 0 && res.data.data) {
    return res.data.data;
  }
  return Promise.reject(new Error(res.data.message));
}

/**
 * 分页查询
 */
export async function queryPage(params) {
  const res = await request.get('/workflow/page', { params });
  if (res.data.code === 0) {
    return res.data.data;
  }
  return Promise.reject(new Error(res.data.message));
}

/**
 * 根据id查询工作流
 */
export async function getWorkFlowInfo(id) {
  const res = await request.get('workflow/' + id);
  if (res.data.code === 0) {
    return res.data.data;
  }
  return Promise.reject(new Error(res.data.message));
}

/**
 * 添加&修改
 */
export async function operation(data) {
  const res = await request.post('/workflow/operation', data);
  if (res.data.code === 0) {
    return res.data.message;
  }
  return Promise.reject(new Error(res.data.message));
}

/**
 * 批量删除
 */
export async function removes(data) {
  const res = await request.post('/workflow/remove', data);
  if (res.data.code === 0) {
    return res.data.message;
  }
  return Promise.reject(new Error(res.data.message));
}
