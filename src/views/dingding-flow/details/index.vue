<template>
  <ele-page hide-footer flex-table>
    <ele-card
      flex-table
      :body-style="{ padding: '6px!important', overflow: 'hidden' }"
    >
      <section
        class="dingflow-design"
        :style="{ height: pageHeight + 'px', overflow: 'auto' }"
      >
        <div class="zoom">
          <div
            class="zoom-out"
            :class="nowVal == 50 && 'disabled'"
            @click="zoomSize(1)"
          ></div>
          <span>{{ nowVal }}%</span>
          <div
            class="zoom-in"
            :class="nowVal == 300 && 'disabled'"
            @click="zoomSize(2)"
          ></div>
        </div>
        <div
          class="box-scale"
          :style="`transform: scale(${nowVal / 100});height:${pageHeight}px`"
        >
          <nodeWrap
            v-model:nodeConfig="nodeConfig"
            v-model:flowPermission="flowPermission"
          />
          <div class="end-node">
            <div class="end-node-circle"></div>
            <div class="end-node-text">流程结束</div>
          </div>
        </div>
      </section>
      <!--      <Workflow :pageHeight="pageHeight" :workflowId="workflowId" @nodeConfig="nodeConfig"/>-->
    </ele-card>
    <!-- 底部工具栏 -->
    <ele-bottom-bar>
      <template #extra>
        <el-button size="small" @click="onBack">取消</el-button>
        <el-button
          size="small"
          type="primary"
          :loading="loading"
          @click="saveSet"
        >
          提交
        </el-button>
      </template>
    </ele-bottom-bar>

    <!-- 审批人设置-->
    <approverDrawer />
    <!-- 条件设置-->
    <conditionDrawer />
  </ele-page>
</template>

<script setup>
  import { onMounted, ref } from 'vue';
  import { usePageTab } from '@/utils/use-page-tab.js';
  import { useRouter } from 'vue-router';
  import { getWorkFlowData } from '@/plugins/api.js';
  import { mapMutations } from '@/plugins/lib.js';
  import approverDrawer from '@/components/DrawFlow/drawer/approverDrawer.vue';
  import conditionDrawer from '@/components/DrawFlow/drawer/conditionDrawer.vue';
  import { getWorkFlowInfo, operation } from '../api/index.js';
  import { EleMessage } from 'ele-admin-plus';

  const { currentRoute, push } = useRouter();
  const { removePageTab, getRouteTabKey, setPageTab } = usePageTab();

  const { setTableId, setIsTried } = mapMutations();

  let workflowId = null;
  let workflowName = null;
  let storageData = localStorage.getItem('WORKFLOW');
  let params = storageData ? JSON.parse(storageData) : null;
  workflowId = params?.workflowId;
  workflowName = params?.workflowName;

  const tipList = ref([]);
  const tipVisible = ref(false);
  const nowVal = ref(100);
  const processConfig = ref({});
  const nodeConfig = ref({});
  const flowPermission = ref([]);
  const setNewConfigData = ref({});
  onMounted(async () => {
    console.log('workflowId===', workflowId);
    if (workflowId) {
      setPageTab({
        key: getRouteTabKey(),
        title: workflowName + '流程设置'
      });

      getWorkFlowInfo(workflowId)
        .then((list) => {
          // console.log(JSON.stringify(list))
          if (list) {
            setNewConfigData.value = {
              tableId: list.id,
              nodeConfig: {
                nodeName: list.name,
                type: 0,
                settype: '',
                conditionDetail: [],
                nodeUserList: null,
                childNode: list.workflowNodes,
                conditionNodes: []
              }
            };
            processConfig.value = setNewConfigData.value;
            setTableId(list.id);
            nodeConfig.value = setNewConfigData.value.nodeConfig;
          }
        })
        .catch((e) => {
          EleMessage.error(e.message);
        });
    } else {
      let { data } = await getWorkFlowData();
      processConfig.value = data;
      let { nodeConfig: nodes, tableId } = data;

      nodeConfig.value = nodes;
      setTableId(tableId);
    }
  });

  const reErr = ({ childNode }) => {
    if (childNode) {
      let { type, error, nodeName, conditionNodes } = childNode;
      if (type === 1 || type === 2) {
        if (error) {
          tipList.value.push({
            name: nodeName,
            type: ['', '审核人', '抄送人'][type]
          });
        }
        reErr(childNode);
      } else if (type == 3) {
        reErr(childNode);
      } else if (type == 4) {
        reErr(childNode);
        for (var i = 0; i < conditionNodes.length; i++) {
          if (conditionNodes[i].error) {
            tipList.value.push({
              name: conditionNodes[i].nodeName,
              type: '条件'
            });
          }
          reErr(conditionNodes[i]);
        }
      }
    } else {
      childNode = null;
    }
  };

  /** 提交状态 */
  const loading = ref(false);
  const saveSet = async () => {
    setIsTried(true);
    tipList.value = [];
    reErr(nodeConfig.value);
    if (tipList.value.length != 0) {
      tipVisible.value = true;
      return;
    } else {
      processConfig.value.flowPermission = flowPermission.value;
      let FlowData = processConfig.value;
      // let workflowNodes = traverseTree(FlowData.nodeConfig.childNode)
      // console.log(JSON.stringify(FlowData.nodeConfig.childNode))
      let workflowData = {
        id: workflowId,
        name: FlowData.nodeConfig.nodeName, //流程名称
        projectId: '', //项目ID
        year: '', //年份
        workflowNodes: FlowData.nodeConfig.childNode
      };
      loading.value = true;
      operation(workflowData)
        .then((msg) => {
          loading.value = false;
          EleMessage.success(msg);
          // emit('done');
        })
        .catch((e) => {
          loading.value = false;
          EleMessage.error(e.message);
        })
        .finally(() => {
          onBack();
          loading.value = false;
        });
    }
  };

  const zoomSize = (type) => {
    if (type == 1) {
      if (nowVal.value == 50) {
        return;
      }
      nowVal.value -= 10;
    } else {
      if (nowVal.value == 300) {
        return;
      }
      nowVal.value += 10;
    }
  };

  /** 返回 */
  const onBack = () => {
    removePageTab({ key: getRouteTabKey() });
    push('/dingding-flow');
  };

  const pageHeight = ref(0);
  onMounted(() => {
    // 获取页面高度
    pageHeight.value =
      document.querySelector('.ele-admin-content').clientHeight - 68;
  });
</script>

<style>
  @import '../../../css/workflow.css';

  .error-modal-list {
    width: 455px;
  }
</style>
