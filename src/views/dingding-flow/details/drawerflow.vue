<template>
  <ele-drawer
    size="84%"
    title="自定义流程设置"
    :append-to-body="true"
    :destroy-on-close="true"
    :close-on-click-modal="false"
    style="max-width: 100%"
    :body-style="{ padding: 'unset!important' }"
    :model-value="modelValue"
    @update:modelValue="updateModelValue"
  >
    <Workflow :pageHeight="pageHeight" />

    <template #footer>
      <el-button @click="updateModelValue(false)">取消</el-button>
      <el-button type="primary" :loading="loading" @click="saveSet">
        保存
      </el-button>
    </template>
  </ele-drawer>
</template>

<script setup>
  import { onMounted, ref } from 'vue';
  import Workflow from '@/views/dingding-flow/components/workflow.vue';

  const emit = defineEmits(['done', 'update:modelValue']);

  /** 更新modelValue */
  const updateModelValue = (value) => {
    emit('update:modelValue', value);
  };

  const pageHeight = ref(0);
  onMounted(() => {
    // 获取页面高度
    pageHeight.value =
      document.querySelector('.ele-admin-content').clientHeight - 20;
  });
</script>
<style>
  @import '../../../css/workflow.css';

  .error-modal-list {
    width: 455px;
  }
</style>
