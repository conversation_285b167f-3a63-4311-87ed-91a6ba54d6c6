import request from '@/utils/request';

const baseUrl = '/unified-auth/admin/business-system';

/**
 * 分页查询
 */
export async function queryPage(params) {
  const res = await request.get(baseUrl + '/page', { params });
  if (res.data.code === 0) {
    return res.data.data;
  }
  return Promise.reject(new Error(res.data.message));
}

/**
 * 根据id查询
 */
export async function getById(id) {
  const res = await request.get(baseUrl + '/' + id);
  if (res.data.code === 0 && res.data.data) {
    return res.data.data;
  }
  return Promise.reject(new Error(res.data.message));
}

/**
 * 添加&修改
 */
export async function operation(data) {
  const res = await request.post(baseUrl + '/operation', data);
  if (res.data.code === 0) {
    return res.data.message;
  }
  return Promise.reject(new Error(res.data.message));
}

/**
 * 批量删除
 */
export async function removes(data) {
  const res = await request.post(baseUrl + '/remove', data);
  if (res.data.code === 0) {
    return res.data.message;
  }
  return Promise.reject(new Error(res.data.message));
}

export async function postReGenerateToken(id) {
  const res = await request.post(baseUrl + '/re-generate-token', `id=${id}`, {
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded'
    }
  });
  if (res.data.code === 0) {
    return res.data.message;
  }
  return Promise.reject(new Error(res.data.message));
}
