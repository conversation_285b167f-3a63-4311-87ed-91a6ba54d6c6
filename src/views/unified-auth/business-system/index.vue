<template>
  <ele-page flex-table>
    <search @search="reload" />
    <ele-card
      :body-style="{ padding: '0 5px 10px 5px!important', overflow: 'hidden' }"
      flex-table
    >
      <!-- 表格 -->
      <ele-pro-table
        ref="tableRef"
        v-model:selections="selections"
        :border="true"
        :columns="columns"
        :datasource="datasource"
        :show-overflow-tooltip="true"
        cache-key="sphfwDictionaryGroup"
        highlight-current-row
        row-key="id"
      >
        <template #toolbar>
          <el-button
            :icon="PlusOutlined"
            class="ele-btn-icon"
            size="small"
            type="primary"
            @click="openEdit()"
          >
            新建
          </el-button>
          <el-button
            :icon="DeleteOutlined"
            class="ele-btn-icon"
            size="small"
            type="danger"
            @click="remove()"
          >
            删除
          </el-button>
        </template>
        <template #action="{ row }">
          <el-link type="primary" @click="openApp(row)">平台</el-link>
          <el-divider direction="vertical" />
          <el-link type="primary" @click="openDetail(row)">详情</el-link>
          <el-divider direction="vertical" />
          <el-link type="primary" @click="reGenerateToken(row)">
            token生成
          </el-link>
          <el-divider direction="vertical" />
          <el-link :underline="false" type="primary" @click="openEdit(row)">
            修改
          </el-link>
          <el-divider direction="vertical" />
          <el-link :underline="false" type="primary" @click="remove(row)">
            删除
          </el-link>
        </template>
        <!-- 用户名表头 -->
        <template #groupNameHeader="{ column }">
          <div style="display: flex; align-items: center">
            <div style="flex: 1; overflow: hidden; text-overflow: ellipsis">
              {{ column.label }}
            </div>
            <name-filter @search="onNameFilter" />
          </div>
        </template>
      </ele-pro-table>
    </ele-card>
    <!-- 编辑弹窗 -->
    <edit v-model="showEdit" :data="current" @done="reload" />
    <Detail :id="currentId" @cancel="currentId = null" />
    <ele-modal
      :body-style="{ padding: '0' }"
      :model-value="!!currentBizId"
      title="平台列表"
      @update:model-value="() => (currentBizId = null)"
    >
      <AppList v-if="currentBizId" :biz-id="currentBizId" />
    </ele-modal>
  </ele-page>
</template>

<script setup>
  import { reactive, ref } from 'vue';
  import { ElMessageBox } from 'element-plus/es';
  import { EleMessage } from 'ele-admin-plus/es';
  import { DeleteOutlined, PlusOutlined } from '@/components/icons';
  import Edit from './components/edit.vue';
  import Detail from './components/detail.vue';
  import nameFilter from './components/name-filter.vue';
  import { postReGenerateToken, queryPage, removes } from './api';
  import Search from './components/search.vue';
  import AppList from '@/views/unified-auth/business-system/platform/app-list.vue';

  const currentBizId = ref(null);
  const openApp = (row) => {
    currentBizId.value = row.id;
  };

  /** 表格实例 */
  const tableRef = ref(null);

  /** 表格列配置 */
  const columns = ref([
    {
      type: 'selection',
      columnKey: 'selection',
      width: 50,
      align: 'center',
      fixed: 'left'
    },
    {
      prop: 'name',
      label: '名称'
    },
    {
      prop: 'type.text',
      label: '类型'
    },
    {
      prop: 'token',
      label: 'token',
      width: 300
    },
    {
      prop: 'updatedAtText',
      label: '更新时间'
    },
    {
      prop: 'createdAtText',
      label: '创建时间'
    },
    {
      columnKey: 'action',
      label: '操作',
      slot: 'action',
      width: 280,
      fixed: 'right'
    }
  ]);

  /** 表格选中数据 */
  const selections = ref([]);

  /** 当前编辑数据 */
  const current = ref(null);

  const currentId = ref(null);

  /** 是否显示编辑弹窗 */
  const showEdit = ref(false);

  /** 表格数据源 */
  const datasource = ({ page, limit, where, orders, filters }) => {
    return queryPage({ ...where, ...orders, ...filters, page, limit });
  };
  /** 用户名筛选值 */
  const nameFilterValue = ref('');

  /** 表格搜索参数 */
  const lastWhere = reactive({});

  /** 用户名筛选事件 */
  const onNameFilter = (name) => {
    nameFilterValue.value = name;
    doReload();
  };
  /** 表格搜索 */
  const doReload = () => {
    if (nameFilterValue.value) {
      reload({
        ...lastWhere,
        groupName: nameFilterValue.value
      });
    } else {
      reload(lastWhere);
    }
  };
  /** 搜索 */
  const reload = (where) => {
    selections.value = [];
    tableRef.value?.reload?.({ page: 1, where });
  };

  /** 打开组group编辑弹窗 */
  const openEdit = (row) => {
    current.value = row ?? null;
    showEdit.value = true;
  };

  const openDetail = (row) => {
    currentId.value = row.id;
  };

  /** 删除单个 */
  const remove = (row) => {
    const rows = row == null ? selections.value : [row];
    if (!rows.length) {
      EleMessage.error('请至少选择一条数据');
      return;
    }
    ElMessageBox.confirm(
      '确定要删除“' + rows.map((d) => d.name).join(', ') + '”吗?',
      '系统提示',
      { type: 'warning', draggable: true }
    )
      .then(() => {
        const loading = EleMessage.loading('请求中..');
        removes(rows.map((d) => d.id))
          .then((msg) => {
            loading.close();
            EleMessage.success(msg);
            reload();
          })
          .catch((e) => {
            loading.close();
            EleMessage.error(e.message);
          });
      })
      .catch(() => {});
  };

  const reGenerateToken = (row) => {
    ElMessageBox.confirm('确定要重新生成token吗?', '系统提示', {
      type: 'warning',
      draggable: true
    })
      .then(() => {
        const loading = EleMessage.loading('请求中..');
        postReGenerateToken(row.id)
          .then((msg) => {
            loading.close();
            EleMessage.success(msg);
            reload();
          })
          .catch((e) => {
            loading.close();
            EleMessage.error(e.message);
          });
      })
      .catch(() => {});
  };
</script>

<script>
  export default {
    name: 'MsgCenterChannelAccount'
  };
</script>
