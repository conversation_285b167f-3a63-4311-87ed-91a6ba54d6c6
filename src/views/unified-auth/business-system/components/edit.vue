<template>
  <ele-drawer
    :append-to-body="true"
    :body-style="{ paddingBottom: '8px' }"
    :destroy-on-close="true"
    :model-value="modelValue"
    :size="530"
    :title="isUpdate ? '修改' : '新建'"
    style="max-width: 100%"
    @update:modelValue="updateModelValue"
  >
    <pro-form
      ref="formRef"
      :grid="{ span: 24 }"
      :items="items"
      :labelWidth="110"
      :model="form"
      @updateValue="setFieldValue"
    />
    <template #footer>
      <el-button @click="updateModelValue(false)">取消</el-button>
      <el-button :loading="loading" type="primary" @click="onSubmit">
        提交
      </el-button>
    </template>
  </ele-drawer>
</template>

<script setup>
  import { ref, watch } from 'vue';
  import { useFormData } from '@/utils/use-form-data';
  import { EleMessage } from 'ele-admin-plus';
  import { operation } from '../api';
  import ProForm from '@/components/ProForm/index.vue';
  // import { useRouter } from 'vue-router';

  // const BASE_URL = import.meta.env.BASE_URL;
  const emit = defineEmits(['done', 'update:modelValue']);

  const props = defineProps({
    /** 弹窗是否打开 */
    modelValue: Boolean,
    /** 修改回显的数据 */
    data: Object
  });

  // const { currentRoute } = useRouter();
  // const { params, path } = unref(currentRoute);
  // const userType = path.split('/')[4];
  /** 是否是修改 */
  const isUpdate = ref(false);

  /** 表单实例 */
  const formRef = ref(null);
  /** 表单数据 */
  const [form, resetFields, assignFields, setFieldValue] = useFormData({
    id: void 0,
    clientName: '',
    clientIndex: '',
    clientId: '',
    clientSecret: '',
    status: ''
  });

  /** 表单项 */
  const items = ref([
    { prop: 'name', label: '名称', type: 'input', required: true },
    {
      prop: 'type',
      label: '类型',
      type: 'select',
      options: [{ value: 'XUE_GONG', label: '学工' }],
      required: true
    }
    // {
    //   prop: 'type',
    //   label: '类型',
    //   type: 'dictSelect',
    //   props: {
    //     code: 'zt',
    //     dicQueryParams: {
    //       getValType: 'name'
    //     }
    //   },
    //   required: true
    // }
  ]);

  /** 提交状态 */
  const loading = ref(false);
  /** 提交 */
  const onSubmit = () => {
    formRef.value?.validate?.((valid) => {
      if (!valid) {
        return;
      }
      // let data = toFormData({...form})
      loading.value = true;
      operation(form)
        .then((msg) => {
          loading.value = false;
          EleMessage.success(msg);
          updateModelValue(false);
          emit('done');
        })
        .catch((e) => {
          loading.value = false;
          EleMessage.error(e.message);
        });
    });
  };

  /** 更新modelValue */
  const updateModelValue = (value) => {
    emit('update:modelValue', value);
  };

  watch(
    () => props.modelValue,
    (modelValue) => {
      if (modelValue) {
        if (props.data) {
          assignFields({
            ...props.data
          });
          isUpdate.value = true;
        } else {
          resetFields();
          formRef.value?.clearValidate?.();
          isUpdate.value = false;
        }
      } else {
        resetFields();
        formRef.value?.clearValidate?.();
      }
    }
  );
</script>
