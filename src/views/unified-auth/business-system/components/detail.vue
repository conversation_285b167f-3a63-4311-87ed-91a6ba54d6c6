<template>
  <ele-modal v-model="visible" :width="460" form title="详情">
    <pro-form :items="items" :labelWidth="110" :model="form" />
    <template #footer>
      <el-button @click="onCancel()">关闭</el-button>
    </template>
  </ele-modal>
</template>

<script setup>
  import { ref, watch } from 'vue';
  import ProForm from '@/components/ProForm/index.vue';
  import { getById } from '@/views/unified-auth/business-system/api/index.js';

  const emit = defineEmits(['cancel']);

  const props = defineProps({
    id: {
      type: [Number, null],
      required: false
    }
  });

  const visible = ref(false);

  watch(
    () => props.id,
    async (id) => {
      if (id) {
        visible.value = true;
        loading.value = true;
        form.value = await getById(id);
        if (form.value && form.value.type) {
          form.value['type-text'] = form.value.type.text;
        }
        loading.value = false;
      } else {
        visible.value = false;
        form.value = {};
      }
    }
  );

  /** 表单数据 */
  const form = ref({});

  /** 表单项 */
  const items = ref([
    { prop: 'name', label: '名称', type: 'text' },
    {
      prop: 'type-text',
      label: '类型',
      type: 'text'
    },
    { prop: 'token', label: 'token', type: 'text' },
    { prop: 'updatedAtText', label: '更新时间', type: 'text' },
    { prop: 'createdAtText', label: '创建时间', type: 'text' }
  ]);

  /** 提交状态 */
  const loading = ref(false);

  /** 更新modelValue */
  const onCancel = (value) => {
    emit('cancel', value);
  };
</script>
