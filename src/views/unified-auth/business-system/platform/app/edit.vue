<template>
  <ele-drawer
    :append-to-body="true"
    :body-style="{ paddingBottom: '8px' }"
    :destroy-on-close="true"
    :model-value="modelValue"
    :size="530"
    :title="isUpdate ? '修改' : '新建'"
    style="max-width: 100%"
    @update:modelValue="updateModelValue"
  >
    <pro-form
      ref="formRef"
      :grid="{ span: 24 }"
      :items="items"
      :labelWidth="110"
      :model="form"
      @updateValue="setFieldValue"
    />
    <template #footer>
      <el-button @click="updateModelValue(false)">取消</el-button>
      <el-button :loading="loading" type="primary" @click="onSubmit">
        提交
      </el-button>
    </template>
  </ele-drawer>
</template>

<script setup>
  import { ref, watch } from 'vue';
  import { useFormData } from '@/utils/use-form-data';
  import { EleMessage } from 'ele-admin-plus';
  import { getById, operation } from '../api/app.js';
  import ProForm from '@/components/ProForm/index.vue';

  const emit = defineEmits(['done', 'update:modelValue']);

  const props = defineProps({
    /** 弹窗是否打开 */
    modelValue: Boolean,
    /** 修改回显的数据 */
    type: String,
    bizId: Number,
    id: {
      type: [Number, null]
    },
    url: String,
    fields: Array
  });

  /** 是否是修改 */
  const isUpdate = ref(false);

  /** 表单实例 */
  const formRef = ref(null);
  /** 表单数据 */
  const [form, resetFields, assignFields, setFieldValue] = useFormData({
    id: void 0,
    name: ''
  });

  const originalFields = [
    { prop: 'name', label: '名称', type: 'input', required: true },
    // {
    //   prop: 'type',
    //   label: '类型',
    //   type: 'select',
    //   options: [{ value: 'XUE_GONG', label: '学工' }],
    //   required: true
    // }
    {
      prop: 'enabled',
      label: '启用',
      type: 'switch',
      default: true,
      required: true
    },
    {
      prop: 'sequenceNum',
      label: '排序号',
      type: 'inputNumber',
      default: 0,
      required: true
    }
  ];

  /** 表单项 */
  const items = ref([]);

  /** 提交状态 */
  const loading = ref(false);
  /** 提交 */
  const onSubmit = () => {
    if (!props.url) {
      return;
    }
    formRef.value?.validate?.((valid) => {
      if (!valid) {
        return;
      }
      // 复制from数据副本至data
      let data = { ...form };
      data.type = {
        id: props.type
      };
      data.bizId = props.bizId;
      loading.value = true;
      operation(props.url + '/operation', data)
        .then((msg) => {
          loading.value = false;
          EleMessage.success(msg);
          updateModelValue(false);
          emit('done');
        })
        .catch((e) => {
          loading.value = false;
          EleMessage.error(e.message);
        });
    });
  };

  /** 更新modelValue */
  const updateModelValue = (value) => {
    emit('update:modelValue', value);
  };

  watch(
    () => props.modelValue,
    (modelValue) => {
      if (modelValue) {
        items.value = [...originalFields].concat(
          props.fields.map((item) => {
            return {
              prop: item.prop,
              label: item.label,
              type: 'input'
            };
          })
        );
        if (props.id) {
          console.log('props.id', props.id);
          if (!props.url) {
            return;
          }
          loading.value = true;
          getById(props.url, props.id)
            .then((data) => {
              console.log('getById data', data);
              loading.value = false;
              let data1 = { ...data };
              delete data1.type;
              delete data1.bizId;
              // assignFields(data1, true);
              Object.assign(form, data1);
              console.log('assignFields', { ...form });
              isUpdate.value = true;
            })
            .catch((e) => {
              loading.value = false;
              EleMessage.error(e.message);
            });
        } else {
          resetFields();
          formRef.value?.clearValidate?.();
          isUpdate.value = false;
        }
      } else {
        Object.assign(form, {
          id: void 0,
          name: ''
        });
        // resetFields();
        formRef.value?.clearValidate?.();
      }
    }
  );
</script>
