<template>
  <ele-modal v-model="visible" :width="460" form title="详情">
    <pro-form :items="items" :labelWidth="110" :model="form" />
    <template #footer>
      <el-button @click="onCancel()">关闭</el-button>
    </template>
  </ele-modal>
</template>

<script setup>
  import { ref, watch } from 'vue';
  import ProForm from '@/components/ProForm/index.vue';
  import { getById } from '../api/app.js';

  const emit = defineEmits(['cancel']);

  const props = defineProps({
    id: {
      type: [Number, null],
      required: false
    },
    url: String,
    fields: Array
  });

  const visible = ref(false);

  watch(
    () => props.id,
    async (id) => {
      if (id) {
        items.value = [...originalFields].concat(
          props.fields.map((item) => {
            return {
              prop: item.prop,
              label: item.label,
              type: 'text'
            };
          })
        );

        visible.value = true;
        if (!props.url) {
          return;
        }

        loading.value = true;
        form.value = await getById(props.url, id);
        if (form.value && form.value.type) {
          form.value['type-text'] = form.value.type.text;
        }
        loading.value = false;
      } else {
        visible.value = false;
        form.value = {};
      }
    }
  );

  /** 表单数据 */
  const form = ref({});

  /** 表单项 */
  const originalFields = [
    { prop: 'type-text', label: '类型', type: 'text' },
    { prop: 'name', label: '名称', type: 'text' },
    {
      prop: 'enabled',
      label: '启用',
      type: 'text'
    },
    {
      prop: 'sequenceNum',
      label: '排序号',
      type: 'text'
    },
    { prop: 'updatedAtText', label: '更新时间', type: 'text' },
    { prop: 'createdAtText', label: '创建时间', type: 'text' }
  ];
  const items = ref([]);

  /** 提交状态 */
  const loading = ref(false);

  /** 更新modelValue */
  const onCancel = (value) => {
    emit('cancel', value);
  };
</script>
