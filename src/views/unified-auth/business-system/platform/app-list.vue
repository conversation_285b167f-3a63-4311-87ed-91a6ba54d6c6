<template>
  <ele-page flex-table>
    <!--    <search @search="reload" />-->
    <ele-card
      :body-style="{ padding: '0 5px 10px 5px!important', overflow: 'hidden' }"
      flex-table
    >
      <!-- 表格 -->
      <ele-pro-table
        ref="tableRef"
        v-model:selections="selections"
        :border="true"
        :columns="columns"
        :datasource="datasource"
        :show-overflow-tooltip="true"
        cache-key="sphfwDictionaryGroup"
        highlight-current-row
        row-key="id"
      >
        <template #toolbar>
          <el-button
            :icon="PlusOutlined"
            class="ele-btn-icon"
            size="small"
            type="primary"
            @click="appTypeSelectModal = true"
          >
            新建
          </el-button>
          <el-button
            :icon="DeleteOutlined"
            class="ele-btn-icon"
            size="small"
            type="danger"
            @click="remove()"
          >
            删除
          </el-button>
        </template>
        <template #action="{ row }">
          <el-link :underline="false" type="primary" @click="openUserList(row)">
            用户
          </el-link>
          <el-divider direction="vertical" />
          <el-link :underline="false" type="primary" @click="openDetail(row)">
            详情
          </el-link>
          <el-divider direction="vertical" />
          <el-link :underline="false" type="primary" @click="openEdit(row)">
            修改
          </el-link>
          <el-divider direction="vertical" />
          <el-link :underline="false" type="primary" @click="remove(row)">
            删除
          </el-link>
        </template>
        <!-- 用户名表头 -->
        <template #groupNameHeader="{ column }">
          <div style="display: flex; align-items: center">
            <div style="flex: 1; overflow: hidden; text-overflow: ellipsis">
              {{ column.label }}
            </div>
            <name-filter @search="onNameFilter" />
          </div>
        </template>
      </ele-pro-table>
    </ele-card>
    <!-- 编辑弹窗 -->
    <edit
      :id="editId"
      :model-value="showEdit"
      :biz-id="props.bizId"
      :type="appTypeSelected"
      :fields="appFields"
      :url="appUrl"
      @done="editDoneFunc"
      @update:model-value="editModalUpdateFunc"
    />
    <ele-modal v-model="appTypeSelectModal" title="选择类型">
      <el-button class="ele-btn-icon" @click="onSelectedAppType('DINGTALK')">
        钉钉
      </el-button>
      <el-button class="ele-btn-icon" @click="onSelectedAppType('WXWORK')">
        企业微信
      </el-button>
      <el-button class="ele-btn-icon" @click="onSelectedAppType('WECHAT')">
        微信公众平台
      </el-button>
      <el-button class="ele-btn-icon" @click="onSelectedAppType('YIBAN')">
        易班
      </el-button>
    </ele-modal>
    <Detail
      :id="detailId"
      :fields="appFields"
      :url="appUrl"
      @cancel="detailId = null"
    />
    <UserList
      :app-id="currentUserListAppId"
      :fields="userListFields"
      :url="userListUrl"
      @close="currentUserListAppId = null"
    />
  </ele-page>
</template>

<script setup>
  import { computed, reactive, ref } from 'vue';
  import { ElMessageBox } from 'element-plus/es';
  import { EleMessage } from 'ele-admin-plus/es';
  import { DeleteOutlined, PlusOutlined } from '@/components/icons';
  import Edit from '@/views/unified-auth/business-system/platform/app/edit.vue';
  import nameFilter from '@/views/unified-auth/business-system/platform/app/name-filter.vue';
  import { queryPage, removes } from './api/app.js';
  import Detail from '@/views/unified-auth/business-system/platform/app/detail.vue';
  import { typeInfo } from '@/views/unified-auth/business-system/platform/util/type-info.js';
  import UserList from '@/views/unified-auth/business-system/platform/user-list.vue';

  // 用户列表
  const currentUserListAppId = ref(null);
  const userListVisible = ref(false);
  const userListFields = computed(() => {
    if (currentTypeInfo.value) {
      return currentTypeInfo.value.userFields;
    }
    return [];
  });
  const userListUrl = computed(() => {
    if (currentTypeInfo.value) {
      return currentTypeInfo.value.url + '/user';
    }
    return '';
  });
  const openUserList = (row) => {
    const type = row.type.id;
    console.log('openUserList type', type);
    switchTypeInfo(type);
    currentUserListAppId.value = row.id;
  };

  // 详情
  const detailId = ref(null);
  const openDetail = (row) => {
    const type = row.type.id;
    console.log('openDetail type', type);
    switchTypeInfo(type);
    detailId.value = row.id;
  };

  const currentTypeInfo = ref(null);
  const appUrl = computed(() => {
    if (currentTypeInfo.value) {
      return currentTypeInfo.value.url + '/app';
    }
    return '';
  });
  const appFields = computed(() => {
    if (currentTypeInfo.value) {
      return currentTypeInfo.value.fields;
    }
    return [];
  });
  const switchTypeInfo = (type) => {
    switch (type) {
      case 'DINGTALK':
        currentTypeInfo.value = typeInfo.DINGTALK;
        break;
      case 'WXWORK':
        currentTypeInfo.value = typeInfo.WXWORK;
        break;
      case 'WECHAT':
        currentTypeInfo.value = typeInfo.WECHAT;
        break;
      case 'YIBAN':
        currentTypeInfo.value = typeInfo.YIBAN;
        break;
      default:
        currentTypeInfo.value = null;
        break;
    }
  };

  const editDoneFunc = () => {
    reload();
  };

  const editModalUpdateFunc = (v) => {
    showEdit.value = v;
    if (!v) {
      appTypeSelected.value = null;
      editId.value = null;
    }
  };

  const appTypeSelected = ref(null);
  const appTypeSelectModal = ref(false);
  const onSelectedAppType = (t) => {
    appTypeSelected.value = t;
    appTypeSelectModal.value = false;
    console.log('appTypeSelected', appTypeSelected.value);
    openEdit();
  };

  const props = defineProps({
    bizId: {
      type: [Number, null],
      required: false
    }
  });
  console.log('app list bizId', props.bizId);

  /** 表格实例 */
  const tableRef = ref(null);

  /** 表格列配置 */
  const columns = ref([
    {
      type: 'selection',
      columnKey: 'selection',
      width: 50,
      align: 'center',
      fixed: 'left'
    },
    {
      prop: 'type.text',
      label: '类型',
      width: 80
    },
    {
      prop: 'name',
      label: '名称'
    },
    { prop: 'enabled', label: '是否启用' },
    { prop: 'sequenceNum', label: '排序号' },
    {
      prop: 'updatedAtText',
      label: '更新时间'
    },
    {
      prop: 'createdAtText',
      label: '创建时间'
    },
    {
      columnKey: 'action',
      label: '操作',
      slot: 'action',
      width: 200,
      fixed: 'right'
    }
  ]);

  /** 表格选中数据 */
  const selections = ref([]);

  /** 是否显示编辑弹窗 */
  const showEdit = ref(false);

  /** 表格数据源 */
  const datasource = ({ page, limit, where, orders, filters }) => {
    return queryPage({
      ...where,
      ...orders,
      ...filters,
      page,
      limit,
      bizId: props.bizId
    });
  };
  /** 用户名筛选值 */
  const nameFilterValue = ref('');

  /** 表格搜索参数 */
  const lastWhere = reactive({});

  /** 用户名筛选事件 */
  const onNameFilter = (name) => {
    nameFilterValue.value = name;
    doReload();
  };
  /** 表格搜索 */
  const doReload = () => {
    if (nameFilterValue.value) {
      reload({
        ...lastWhere,
        groupName: nameFilterValue.value
      });
    } else {
      reload(lastWhere);
    }
  };
  /** 搜索 */
  const reload = (where) => {
    selections.value = [];
    tableRef.value?.reload?.({ page: 1, where });
  };

  /** 打开组group编辑弹窗 */
  const editId = ref(null);
  const openEdit = (row) => {
    if (row) {
      editId.value = row.id;
      appTypeSelected.value = row.type.id;
    }
    const type = appTypeSelected.value;
    switchTypeInfo(type);
    showEdit.value = true;
  };

  /** 删除单个 */
  const remove = (row) => {
    const rows = row == null ? selections.value : [row];
    if (!rows.length) {
      EleMessage.error('请至少选择一条数据');
      return;
    }
    ElMessageBox.confirm(
      '确定要删除“' + rows.map((d) => d.name).join(', ') + '”吗?',
      '系统提示',
      { type: 'warning', draggable: true }
    )
      .then(() => {
        const loading = EleMessage.loading('请求中..');
        removes(rows.map((d) => d.id))
          .then((msg) => {
            loading.close();
            EleMessage.success(msg);
            reload();
          })
          .catch((e) => {
            loading.close();
            EleMessage.error(e.message);
          });
      })
      .catch(() => {});
  };
</script>

<script>
  export default {
    name: 'MsgCenterChannelAccount'
  };
</script>
