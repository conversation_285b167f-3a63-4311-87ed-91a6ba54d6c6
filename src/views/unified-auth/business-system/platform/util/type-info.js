export const typeInfo = {
  DINGTALK: {
    url: '/unified-auth/admin/dingtalk',
    fields: [
      {
        prop: 'corpId',
        label: 'CorpId'
      },
      {
        prop: 'unifiedAppId',
        label: 'App ID'
      },
      {
        prop: 'agentId',
        label: 'AgentId'
      },
      {
        prop: 'clientId',
        label: 'Client ID'
      },
      {
        prop: 'clientSecret',
        label: 'Client Secret'
      }
    ],
    userFields: [
      {
        prop: 'bizUserId',
        label: '业务用户ID'
      },
      {
        prop: 'userId',
        label: '用户ID'
      },
      {
        prop: 'name',
        label: '用户名称'
      }
    ]
  },
  WXWORK: {
    url: '/unified-auth/admin/wxwork',
    fields: [
      {
        prop: 'corpId',
        label: '企业ID'
      },
      {
        prop: 'agentId',
        label: 'AgentId'
      },
      {
        prop: 'secret',
        label: 'Secret'
      }
    ],
    userFields: [
      {
        prop: 'bizUserId',
        label: '业务用户ID'
      },
      {
        prop: 'userId',
        label: '账号'
      }
    ]
  },
  WECHAT: {
    url: '/unified-auth/admin/wechat',
    fields: [
      {
        prop: 'appId',
        label: 'App ID'
      },
      {
        prop: 'appSecret',
        label: 'App Secret'
      }
    ],
    userFields: [
      {
        prop: 'bizUserId',
        label: '业务用户ID'
      },
      {
        prop: 'openId',
        label: 'openId'
      }
    ]
  },
  YIBAN: {
    url: '/unified-auth/admin/yiban',
    fields: [
      {
        prop: 'appId',
        label: 'App ID'
      },
      {
        prop: 'appSecret',
        label: 'App Secret'
      }
    ],
    userFields: [
      {
        prop: 'bizUserId',
        label: '业务用户ID'
      },
      {
        prop: 'userId',
        label: '用户Id'
      },
      {
        prop: 'username',
        label: '用户名'
      },
      {
        prop: 'tester',
        label: '是否测试用户',
        type: 'switch'
      }
    ]
  }
};
