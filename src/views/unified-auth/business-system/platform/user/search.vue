<!-- 搜索表单 -->
<template>
  <el-form
    :inline="true"
    label-width="auto"
    size="small"
    @keyup.enter="search"
    @submit.prevent=""
  >
    <!--    <el-form-item label="名称">-->
    <!--      <el-input v-model.trim="form.nameLike" clearable placeholder="请输入" />-->
    <!--    </el-form-item>-->
    <el-form-item
      v-for="field in props.fields"
      :key="field.prop"
      :label="field.label"
    >
      <el-select
        v-if="field.type === 'switch'"
        v-model="tempFormData[field.prop]"
        placeholder="请选择"
      >
        <el-option label="..." value="" />
        <el-option label="是" value="true" />
        <el-option label="否" value="false" />
      </el-select>
      <el-input
        v-else
        v-model.trim="tempFormData[field.prop]"
        clearable
        placeholder="请输入"
      />
    </el-form-item>
    <el-form-item label-width="16px">
      <el-button type="primary" @click="search">查询</el-button>
      <el-button @click="reset">重置</el-button>
    </el-form-item>
  </el-form>
</template>

<script setup>
  import { ref, watch } from 'vue';

  const props = defineProps({
    fields: Array
  });

  const tempFormData = ref({});

  const emit = defineEmits(['search']);

  const resetFields = () => {
    tempFormData.value = {};
  };

  /** 搜索 */
  const search = () => {
    console.log(tempFormData.value);
    emit('search', { ...tempFormData.value });
  };

  /**  重置 */
  const reset = () => {
    resetFields();
    search();
  };

  watch(
    () => props.fields,
    (fields) => {
      console.log('watch fields change', fields);
      reset();
    }
  );
</script>

<style scoped>
  .el-form--inline {
    .el-form-item {
      .el-input,
      .el-cascader,
      .el-select,
      .el-autocomplete {
        width: 140px;
      }
    }
  }
</style>
