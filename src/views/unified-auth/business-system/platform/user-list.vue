<template>
  <ele-modal
    :body-style="{ padding: '0' }"
    :model-value="!!props.appId"
    title="用户列表"
    @update:model-value="modalClose"
    :width="900"
  >
    <ele-page flex-table>
      <search :fields="props.fields" @search="reload" />
      <ele-card
        :body-style="{
          padding: '0 5px 10px 5px!important',
          overflow: 'hidden'
        }"
        flex-table
      >
        <!-- 表格 -->
        <ele-pro-table
          ref="tableRef"
          v-model:selections="selections"
          :border="true"
          :columns="columns1"
          :datasource="datasource"
          :show-overflow-tooltip="true"
          highlight-current-row
          row-key="id"
        >
          <template #toolbar>
            <el-button
              :icon="PlusOutlined"
              class="ele-btn-icon"
              size="small"
              type="primary"
              @click="openEdit"
            >
              新建
            </el-button>
            <el-button
              :icon="DeleteOutlined"
              class="ele-btn-icon"
              size="small"
              type="danger"
              @click="remove()"
            >
              删除
            </el-button>
          </template>
          <template #action="{ row }">
            <el-link :underline="false" type="primary" @click="openEdit(row)">
              修改
            </el-link>
            <el-divider direction="vertical" />
            <el-link :underline="false" type="primary" @click="remove(row)">
              删除
            </el-link>
          </template>
        </ele-pro-table>
      </ele-card>
      <!-- 编辑弹窗 -->
      <edit
        :id="editId"
        :app-id="props.appId"
        :fields="props.fields"
        :model-value="showEdit"
        :url="props.url"
        @done="editDoneFunc"
        @update:model-value="editModalUpdateFunc"
      />
    </ele-page>
  </ele-modal>
</template>

<script setup>
  import { computed, ref } from 'vue';
  import { ElMessageBox } from 'element-plus/es';
  import { EleMessage } from 'ele-admin-plus/es';
  import { DeleteOutlined, PlusOutlined } from '@/components/icons';
  import Edit from '@/views/unified-auth/business-system/platform/user/edit.vue';
  import { queryPage, removes } from './api/user.js';
  import Search from './user/search.vue';

  const emit = defineEmits(['close']);
  function modalClose(v) {
    if (!v) {
      emit('close');
    }
  }

  const editDoneFunc = () => {
    reload();
  };

  const editModalUpdateFunc = (v) => {
    showEdit.value = v;
    if (!v) {
      editId.value = null;
    }
  };

  const props = defineProps({
    appId: {
      type: [Number, null],
      required: false
    },
    url: String,
    fields: Array
  });
  console.log('user list appId', props.appId);

  // watch(
  //   () => props.appId,
  //   async (id) => {
  //     console.log('user-list.vue props.url', props.url);
  //     if (id) {
  //       reload();
  //     }
  //   }
  // );

  /** 表格实例 */
  const tableRef = ref(null);

  /** 表格列配置 */
  const originalColumns = [
    {
      prop: 'updatedAtText',
      label: '更新时间'
    },
    {
      prop: 'createdAtText',
      label: '创建时间'
    },
    {
      columnKey: 'action',
      label: '操作',
      slot: 'action',
      width: 100,
      fixed: 'right'
    }
  ];
  const columns1 = computed(() => {
    return [
      {
        type: 'selection',
        columnKey: 'selection',
        width: 50,
        align: 'center',
        fixed: 'left'
      },
      ...props.fields,
      ...originalColumns
    ];
  });

  /** 表格选中数据 */
  const selections = ref([]);

  /** 是否显示编辑弹窗 */
  const showEdit = ref(false);

  /** 表格数据源 */
  const datasource = ({ page, limit, where, orders, filters }) => {
    return queryPage(props.url, {
      ...where,
      ...orders,
      ...filters,
      page,
      limit,
      appId: props.appId
    });
  };

  /** 搜索 */
  const reload = (where) => {
    selections.value = [];
    tableRef.value?.reload?.({ page: 1, where });
  };

  /** 打开组group编辑弹窗 */
  const editId = ref(null);
  const openEdit = (row) => {
    if (row) {
      editId.value = row.id;
    }
    showEdit.value = true;
  };

  /** 删除单个 */
  const remove = (row) => {
    const rows = row == null ? selections.value : [row];
    if (!rows.length) {
      EleMessage.error('请至少选择一条数据');
      return;
    }
    ElMessageBox.confirm(
      '确定要删除“' + rows.map((d) => d.bizUserId).join(', ') + '”吗?',
      '系统提示',
      { type: 'warning', draggable: true }
    )
      .then(() => {
        const loading = EleMessage.loading('请求中..');
        removes(
          props.url,
          rows.map((d) => d.id)
        )
          .then((msg) => {
            loading.close();
            EleMessage.success(msg);
            reload();
          })
          .catch((e) => {
            loading.close();
            EleMessage.error(e.message);
          });
      })
      .catch(() => {});
  };
</script>

<script>
  export default {
    name: 'PlatformUserList'
  };
</script>
