<template>
  <ele-page class="workplace-page">
    <!-- AI助手欢迎卡片 -->
    <ele-card class="welcome-card">
      <div class="welcome-header">
        <div class="welcome-title">
          <h1>AI 助手服务平台</h1>
          <p>让AI助力您的工作更高效</p>
        </div>
        <!-- <div class="welcome-stats">
          <div class="stat-item">
            <div class="stat-number">1280+</div>
            <div class="stat-label">服务用户</div>
          </div>
          <div class="stat-item">
            <div class="stat-number">98%</div>
            <div class="stat-label">满意度</div>
          </div>
          <div class="stat-item">
            <div class="stat-number">24h</div>
            <div class="stat-label">在线服务</div>
          </div>
        </div> -->
      </div>
    </ele-card>

    <!-- AI功能卡片区 -->
    <!-- <el-row :gutter="16">
      <el-col :md="8" :sm="12" :xs="24" v-for="service in aiServices" :key="service.id">
        <ele-card class="service-card" :body-style="{ padding: '20px' }">
          <div class="service-icon">
            <component :is="service.icon"/>
          </div>
          <h3>{{ service.title }}</h3>
          <p>{{ service.description }}</p>
          <el-button type="primary" round>立即使用</el-button>
        </ele-card>
      </el-col>
    </el-row> -->

    <!-- 最新动态区域 -->
    <ele-card class="news-card" title="平台动态">
      <el-timeline>
        <el-timeline-item
          v-for="(activity, index) in activities"
          :key="index"
          :timestamp="activity.timestamp"
          :type="activity.type"
        >
          {{ activity.content }}
        </el-timeline-item>
      </el-timeline>
    </ele-card>
  </ele-page>
</template>

<script setup>
  import { ref } from 'vue';
  import {
    ChatDotRound,
    EditPen,
    DataLine,
    Tools
  } from '@element-plus/icons-vue';

  // AI服务数据
  const aiServices = ref([
    {
      id: 1,
      title: '智能对话',
      description: '基于先进的语言模型,为您提供智能、精准的对话服务',
      icon: ChatDotRound
    },
    {
      id: 2,
      title: '内容创作',
      description: '轻松生成高质量文案、报告、代码等多种类型内容',
      icon: EditPen
    },
    {
      id: 3,
      title: '数据分析',
      description: '快速分析数据,生成专业的可视化图表和分析报告',
      icon: DataLine
    },
    {
      id: 4,
      title: 'AI工具集',
      description: '提供图像处理、语音转换等多样化AI工具',
      icon: Tools
    }
  ]);

  // 平台动态数据
  const activities = ref([
    {
      content: '新增知识库支持',
      timestamp: '2024-03-05',
      type: 'success'
    },
    {
      content: '升级对话系统,响应速度提升50%',
      timestamp: '2024-03-01',
      type: 'info'
    },
    {
      content: '新增多模型接入',
      timestamp: '2024-02-10',
      type: 'warning'
    }
  ]);
</script>

<style lang="scss" scoped>
  .workplace-page {
    .welcome-card {
      margin-bottom: 20px;

      .welcome-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 20px;

        @media (max-width: 768px) {
          flex-direction: column;
          text-align: center;
        }
      }

      .welcome-title {
        h1 {
          font-size: 28px;
          color: #303133;
          margin: 0 0 10px 0;
        }

        p {
          font-size: 16px;
          color: #909399;
          margin: 0;
        }
      }

      .welcome-stats {
        display: flex;
        gap: 40px;

        .stat-item {
          text-align: center;

          .stat-number {
            font-size: 24px;
            font-weight: bold;
            color: #409eff;
          }

          .stat-label {
            font-size: 14px;
            color: #909399;
          }
        }
      }
    }

    .service-card {
      height: 100%;
      text-align: center;
      margin-bottom: 20px;
      transition: all 0.3s;

      &:hover {
        transform: translateY(-5px);
        box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
      }

      .service-icon {
        color: #409eff;
        margin-bottom: 15px;
      }

      h3 {
        margin: 10px 0;
        color: #303133;
      }

      p {
        color: #909399;
        margin-bottom: 20px;
        min-height: 40px;
      }
    }

    .news-card {
      margin-top: 20px;
    }
  }
</style>
