[2m2025-08-02 09:16:20.076[0;39m [32m INFO[0;39m [35m8258[0;39m [2m---[0;39m [2m[kground-preinit][0;39m [36mo.h.validator.internal.util.Version     [0;39m [2m:[0;39m HV000001: Hibernate Validator 8.0.2.Final
[2m2025-08-02 09:16:20.463[0;39m [32m INFO[0;39m [35m8258[0;39m [2m---[0;39m [2m[           main][0;39m [36mcom.sanyth.App                          [0;39m [2m:[0;39m Starting App using Java 17.0.15 with PID 8258 (/Users/<USER>/WorkSpace/sanyth-dev/sanyth-dev-platform/sanyth-server/target/classes started by paynexc in /Users/<USER>/WorkSpace/sanyth-dev)
[2m2025-08-02 09:16:20.463[0;39m [32mDEBUG[0;39m [35m8258[0;39m [2m---[0;39m [2m[           main][0;39m [36mcom.sanyth.App                          [0;39m [2m:[0;39m Running with Spring Boot v3.5.3, Spring v6.2.8
[2m2025-08-02 09:16:20.464[0;39m [32m INFO[0;39m [35m8258[0;39m [2m---[0;39m [2m[           main][0;39m [36mcom.sanyth.App                          [0;39m [2m:[0;39m The following 1 profile is active: "dev"
[2m2025-08-02 09:16:20.564[0;39m [32m INFO[0;39m [35m8258[0;39m [2m---[0;39m [2m[           main][0;39m [36mo.s.b.devtools.restart.ChangeableUrls   [0;39m [2m:[0;39m The Class-Path manifest attribute in /Users/<USER>/.m2/repository/javax/mail/mail/1.4/mail-1.4.jar referenced one or more files that do not exist: file:/Users/<USER>/.m2/repository/javax/mail/mail/1.4/activation.jar
[2m2025-08-02 09:16:20.564[0;39m [32m INFO[0;39m [35m8258[0;39m [2m---[0;39m [2m[           main][0;39m [36mo.s.b.devtools.restart.ChangeableUrls   [0;39m [2m:[0;39m The Class-Path manifest attribute in /Users/<USER>/.m2/repository/com/oracle/ojdbc8/23.2.0.0.0/ojdbc8-23.2.0.0.0.jar referenced one or more files that do not exist: file:/Users/<USER>/.m2/repository/com/oracle/ojdbc8/23.2.0.0.0/oraclepki.jar
[2m2025-08-02 09:16:20.564[0;39m [32m INFO[0;39m [35m8258[0;39m [2m---[0;39m [2m[           main][0;39m [36m.e.DevToolsPropertyDefaultsPostProcessor[0;39m [2m:[0;39m Devtools property defaults active! Set 'spring.devtools.add-properties' to 'false' to disable
[2m2025-08-02 09:16:20.564[0;39m [32m INFO[0;39m [35m8258[0;39m [2m---[0;39m [2m[           main][0;39m [36m.e.DevToolsPropertyDefaultsPostProcessor[0;39m [2m:[0;39m For additional web related logging consider setting the 'logging.level.web' property to 'DEBUG'
[2m2025-08-02 09:16:23.577[0;39m [32m INFO[0;39m [35m8258[0;39m [2m---[0;39m [2m[           main][0;39m [36m.s.d.r.c.RepositoryConfigurationDelegate[0;39m [2m:[0;39m Multiple Spring Data modules found, entering strict repository configuration mode
[2m2025-08-02 09:16:23.582[0;39m [32m INFO[0;39m [35m8258[0;39m [2m---[0;39m [2m[           main][0;39m [36m.s.d.r.c.RepositoryConfigurationDelegate[0;39m [2m:[0;39m Bootstrapping Spring Data JPA repositories in DEFAULT mode.
[2m2025-08-02 09:16:24.141[0;39m [32m INFO[0;39m [35m8258[0;39m [2m---[0;39m [2m[           main][0;39m [36m.s.d.r.c.RepositoryConfigurationDelegate[0;39m [2m:[0;39m Finished Spring Data repository scanning in 535 ms. Found 10 JPA repository interfaces.
[2m2025-08-02 09:16:24.151[0;39m [32m INFO[0;39m [35m8258[0;39m [2m---[0;39m [2m[           main][0;39m [36m.s.d.r.c.RepositoryConfigurationDelegate[0;39m [2m:[0;39m Multiple Spring Data modules found, entering strict repository configuration mode
[2m2025-08-02 09:16:24.151[0;39m [32m INFO[0;39m [35m8258[0;39m [2m---[0;39m [2m[           main][0;39m [36m.s.d.r.c.RepositoryConfigurationDelegate[0;39m [2m:[0;39m Bootstrapping Spring Data MongoDB repositories in DEFAULT mode.
[2m2025-08-02 09:16:24.182[0;39m [32m INFO[0;39m [35m8258[0;39m [2m---[0;39m [2m[           main][0;39m [36m.RepositoryConfigurationExtensionSupport[0;39m [2m:[0;39m Spring Data MongoDB - Could not safely identify store assignment for repository candidate interface com.sanyth.unified_auth.common.repository.BusinessSystemRepository; If you want this repository to be a MongoDB repository, consider annotating your entities with one of these annotations: org.springframework.data.mongodb.core.mapping.Document (preferred), or consider extending one of the following types with your repository: org.springframework.data.mongodb.repository.MongoRepository
[2m2025-08-02 09:16:24.183[0;39m [32m INFO[0;39m [35m8258[0;39m [2m---[0;39m [2m[           main][0;39m [36m.RepositoryConfigurationExtensionSupport[0;39m [2m:[0;39m Spring Data MongoDB - Could not safely identify store assignment for repository candidate interface com.sanyth.unified_auth.platform.dingtalk.repository.DingtalkAppRepository; If you want this repository to be a MongoDB repository, consider annotating your entities with one of these annotations: org.springframework.data.mongodb.core.mapping.Document (preferred), or consider extending one of the following types with your repository: org.springframework.data.mongodb.repository.MongoRepository
[2m2025-08-02 09:16:24.183[0;39m [32m INFO[0;39m [35m8258[0;39m [2m---[0;39m [2m[           main][0;39m [36m.RepositoryConfigurationExtensionSupport[0;39m [2m:[0;39m Spring Data MongoDB - Could not safely identify store assignment for repository candidate interface com.sanyth.unified_auth.platform.dingtalk.repository.DingtalkUserRepository; If you want this repository to be a MongoDB repository, consider annotating your entities with one of these annotations: org.springframework.data.mongodb.core.mapping.Document (preferred), or consider extending one of the following types with your repository: org.springframework.data.mongodb.repository.MongoRepository
[2m2025-08-02 09:16:24.183[0;39m [32m INFO[0;39m [35m8258[0;39m [2m---[0;39m [2m[           main][0;39m [36m.RepositoryConfigurationExtensionSupport[0;39m [2m:[0;39m Spring Data MongoDB - Could not safely identify store assignment for repository candidate interface com.sanyth.unified_auth.platform.repository.PlatformAppRepository; If you want this repository to be a MongoDB repository, consider annotating your entities with one of these annotations: org.springframework.data.mongodb.core.mapping.Document (preferred), or consider extending one of the following types with your repository: org.springframework.data.mongodb.repository.MongoRepository
[2m2025-08-02 09:16:24.183[0;39m [32m INFO[0;39m [35m8258[0;39m [2m---[0;39m [2m[           main][0;39m [36m.RepositoryConfigurationExtensionSupport[0;39m [2m:[0;39m Spring Data MongoDB - Could not safely identify store assignment for repository candidate interface com.sanyth.unified_auth.platform.wechat.repository.WechatAppRepository; If you want this repository to be a MongoDB repository, consider annotating your entities with one of these annotations: org.springframework.data.mongodb.core.mapping.Document (preferred), or consider extending one of the following types with your repository: org.springframework.data.mongodb.repository.MongoRepository
[2m2025-08-02 09:16:24.183[0;39m [32m INFO[0;39m [35m8258[0;39m [2m---[0;39m [2m[           main][0;39m [36m.RepositoryConfigurationExtensionSupport[0;39m [2m:[0;39m Spring Data MongoDB - Could not safely identify store assignment for repository candidate interface com.sanyth.unified_auth.platform.wechat.repository.WechatUserRepository; If you want this repository to be a MongoDB repository, consider annotating your entities with one of these annotations: org.springframework.data.mongodb.core.mapping.Document (preferred), or consider extending one of the following types with your repository: org.springframework.data.mongodb.repository.MongoRepository
[2m2025-08-02 09:16:24.183[0;39m [32m INFO[0;39m [35m8258[0;39m [2m---[0;39m [2m[           main][0;39m [36m.RepositoryConfigurationExtensionSupport[0;39m [2m:[0;39m Spring Data MongoDB - Could not safely identify store assignment for repository candidate interface com.sanyth.unified_auth.platform.wxwork.repository.WxworkAppRepository; If you want this repository to be a MongoDB repository, consider annotating your entities with one of these annotations: org.springframework.data.mongodb.core.mapping.Document (preferred), or consider extending one of the following types with your repository: org.springframework.data.mongodb.repository.MongoRepository
[2m2025-08-02 09:16:24.183[0;39m [32m INFO[0;39m [35m8258[0;39m [2m---[0;39m [2m[           main][0;39m [36m.RepositoryConfigurationExtensionSupport[0;39m [2m:[0;39m Spring Data MongoDB - Could not safely identify store assignment for repository candidate interface com.sanyth.unified_auth.platform.wxwork.repository.WxworkUserRepository; If you want this repository to be a MongoDB repository, consider annotating your entities with one of these annotations: org.springframework.data.mongodb.core.mapping.Document (preferred), or consider extending one of the following types with your repository: org.springframework.data.mongodb.repository.MongoRepository
[2m2025-08-02 09:16:24.184[0;39m [32m INFO[0;39m [35m8258[0;39m [2m---[0;39m [2m[           main][0;39m [36m.RepositoryConfigurationExtensionSupport[0;39m [2m:[0;39m Spring Data MongoDB - Could not safely identify store assignment for repository candidate interface com.sanyth.unified_auth.platform.yiban.repository.YibanAppRepository; If you want this repository to be a MongoDB repository, consider annotating your entities with one of these annotations: org.springframework.data.mongodb.core.mapping.Document (preferred), or consider extending one of the following types with your repository: org.springframework.data.mongodb.repository.MongoRepository
[2m2025-08-02 09:16:24.184[0;39m [32m INFO[0;39m [35m8258[0;39m [2m---[0;39m [2m[           main][0;39m [36m.RepositoryConfigurationExtensionSupport[0;39m [2m:[0;39m Spring Data MongoDB - Could not safely identify store assignment for repository candidate interface com.sanyth.unified_auth.platform.yiban.repository.YibanUserRepository; If you want this repository to be a MongoDB repository, consider annotating your entities with one of these annotations: org.springframework.data.mongodb.core.mapping.Document (preferred), or consider extending one of the following types with your repository: org.springframework.data.mongodb.repository.MongoRepository
[2m2025-08-02 09:16:24.184[0;39m [32m INFO[0;39m [35m8258[0;39m [2m---[0;39m [2m[           main][0;39m [36m.s.d.r.c.RepositoryConfigurationDelegate[0;39m [2m:[0;39m Finished Spring Data repository scanning in 31 ms. Found 0 MongoDB repository interfaces.
[2m2025-08-02 09:16:24.195[0;39m [32m INFO[0;39m [35m8258[0;39m [2m---[0;39m [2m[           main][0;39m [36m.s.d.r.c.RepositoryConfigurationDelegate[0;39m [2m:[0;39m Multiple Spring Data modules found, entering strict repository configuration mode
[2m2025-08-02 09:16:24.196[0;39m [32m INFO[0;39m [35m8258[0;39m [2m---[0;39m [2m[           main][0;39m [36m.s.d.r.c.RepositoryConfigurationDelegate[0;39m [2m:[0;39m Bootstrapping Spring Data Redis repositories in DEFAULT mode.
[2m2025-08-02 09:16:24.239[0;39m [32m INFO[0;39m [35m8258[0;39m [2m---[0;39m [2m[           main][0;39m [36m.RepositoryConfigurationExtensionSupport[0;39m [2m:[0;39m Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.sanyth.unified_auth.common.repository.BusinessSystemRepository; If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository
[2m2025-08-02 09:16:24.239[0;39m [32m INFO[0;39m [35m8258[0;39m [2m---[0;39m [2m[           main][0;39m [36m.RepositoryConfigurationExtensionSupport[0;39m [2m:[0;39m Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.sanyth.unified_auth.platform.dingtalk.repository.DingtalkAppRepository; If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository
[2m2025-08-02 09:16:24.239[0;39m [32m INFO[0;39m [35m8258[0;39m [2m---[0;39m [2m[           main][0;39m [36m.RepositoryConfigurationExtensionSupport[0;39m [2m:[0;39m Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.sanyth.unified_auth.platform.dingtalk.repository.DingtalkUserRepository; If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository
[2m2025-08-02 09:16:24.239[0;39m [32m INFO[0;39m [35m8258[0;39m [2m---[0;39m [2m[           main][0;39m [36m.RepositoryConfigurationExtensionSupport[0;39m [2m:[0;39m Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.sanyth.unified_auth.platform.repository.PlatformAppRepository; If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository
[2m2025-08-02 09:16:24.239[0;39m [32m INFO[0;39m [35m8258[0;39m [2m---[0;39m [2m[           main][0;39m [36m.RepositoryConfigurationExtensionSupport[0;39m [2m:[0;39m Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.sanyth.unified_auth.platform.wechat.repository.WechatAppRepository; If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository
[2m2025-08-02 09:16:24.239[0;39m [32m INFO[0;39m [35m8258[0;39m [2m---[0;39m [2m[           main][0;39m [36m.RepositoryConfigurationExtensionSupport[0;39m [2m:[0;39m Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.sanyth.unified_auth.platform.wechat.repository.WechatUserRepository; If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository
[2m2025-08-02 09:16:24.240[0;39m [32m INFO[0;39m [35m8258[0;39m [2m---[0;39m [2m[           main][0;39m [36m.RepositoryConfigurationExtensionSupport[0;39m [2m:[0;39m Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.sanyth.unified_auth.platform.wxwork.repository.WxworkAppRepository; If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository
[2m2025-08-02 09:16:24.240[0;39m [32m INFO[0;39m [35m8258[0;39m [2m---[0;39m [2m[           main][0;39m [36m.RepositoryConfigurationExtensionSupport[0;39m [2m:[0;39m Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.sanyth.unified_auth.platform.wxwork.repository.WxworkUserRepository; If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository
[2m2025-08-02 09:16:24.240[0;39m [32m INFO[0;39m [35m8258[0;39m [2m---[0;39m [2m[           main][0;39m [36m.RepositoryConfigurationExtensionSupport[0;39m [2m:[0;39m Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.sanyth.unified_auth.platform.yiban.repository.YibanAppRepository; If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository
[2m2025-08-02 09:16:24.240[0;39m [32m INFO[0;39m [35m8258[0;39m [2m---[0;39m [2m[           main][0;39m [36m.RepositoryConfigurationExtensionSupport[0;39m [2m:[0;39m Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.sanyth.unified_auth.platform.yiban.repository.YibanUserRepository; If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository
[2m2025-08-02 09:16:24.240[0;39m [32m INFO[0;39m [35m8258[0;39m [2m---[0;39m [2m[           main][0;39m [36m.s.d.r.c.RepositoryConfigurationDelegate[0;39m [2m:[0;39m Finished Spring Data repository scanning in 31 ms. Found 0 Redis repository interfaces.
[2m2025-08-02 09:16:25.833[0;39m [32m INFO[0;39m [35m8258[0;39m [2m---[0;39m [2m[           main][0;39m [36mo.s.b.w.embedded.tomcat.TomcatWebServer [0;39m [2m:[0;39m Tomcat initialized with port 9096 (http)
[2m2025-08-02 09:16:25.898[0;39m [32m INFO[0;39m [35m8258[0;39m [2m---[0;39m [2m[           main][0;39m [36mo.a.coyote.http11.Http11NioProtocol     [0;39m [2m:[0;39m Initializing ProtocolHandler ["http-nio-9096"]
[2m2025-08-02 09:16:25.902[0;39m [32m INFO[0;39m [35m8258[0;39m [2m---[0;39m [2m[           main][0;39m [36mo.apache.catalina.core.StandardService  [0;39m [2m:[0;39m Starting service [Tomcat]
[2m2025-08-02 09:16:25.902[0;39m [32m INFO[0;39m [35m8258[0;39m [2m---[0;39m [2m[           main][0;39m [36mo.apache.catalina.core.StandardEngine   [0;39m [2m:[0;39m Starting Servlet engine: [Apache Tomcat/10.1.42]
[2m2025-08-02 09:16:26.005[0;39m [32m INFO[0;39m [35m8258[0;39m [2m---[0;39m [2m[           main][0;39m [36mo.a.c.c.C.[Tomcat].[localhost].[/]      [0;39m [2m:[0;39m Initializing Spring embedded WebApplicationContext
[2m2025-08-02 09:16:26.005[0;39m [32m INFO[0;39m [35m8258[0;39m [2m---[0;39m [2m[           main][0;39m [36mw.s.c.ServletWebServerApplicationContext[0;39m [2m:[0;39m Root WebApplicationContext: initialization completed in 5440 ms
[2m2025-08-02 09:16:26.737[0;39m [32m INFO[0;39m [35m8258[0;39m [2m---[0;39m [2m[           main][0;39m [36morg.mongodb.driver.client               [0;39m [2m:[0;39m MongoClient with metadata {"driver": {"name": "mongo-java-driver|sync|spring-boot", "version": "5.5.1"}, "os": {"type": "Darwin", "name": "Mac OS X", "architecture": "aarch64", "version": "15.5"}, "platform": "Java/Azul Systems, Inc./17.0.15+6-LTS"} created with settings MongoClientSettings{readPreference=primary, writeConcern=WriteConcern{w=null, wTimeout=null ms, journal=null}, retryWrites=true, retryReads=true, readConcern=ReadConcern{level=null}, credential=MongoCredential{mechanism=null, userName='admin', source='admin', password=<hidden>, mechanismProperties=<hidden>}, transportSettings=null, commandListeners=[io.micrometer.core.instrument.binder.mongodb.MongoMetricsCommandListener@41af7f0d], codecRegistry=ProvidersCodecRegistry{codecProviders=[ValueCodecProvider{}, BsonValueCodecProvider{}, DBRefCodecProvider{}, DBObjectCodecProvider{}, DocumentCodecProvider{}, CollectionCodecProvider{}, IterableCodecProvider{}, MapCodecProvider{}, GeoJsonCodecProvider{}, GridFSFileCodecProvider{}, Jsr310CodecProvider{}, JsonObjectCodecProvider{}, BsonCodecProvider{}, EnumCodecProvider{}, com.mongodb.client.model.mql.ExpressionCodecProvider@fe3a7f2, com.mongodb.Jep395RecordCodecProvider@2817977a, com.mongodb.KotlinCodecProvider@3aceaf5b]}, loggerSettings=LoggerSettings{maxDocumentLength=1000}, clusterSettings={hosts=[*************:37017], srvServiceName=mongodb, mode=SINGLE, requiredClusterType=UNKNOWN, requiredReplicaSetName='null', serverSelector='null', clusterListeners='[]', serverSelectionTimeout='30000 ms', localThreshold='15 ms'}, socketSettings=SocketSettings{connectTimeoutMS=10000, readTimeoutMS=0, receiveBufferSize=0, proxySettings=ProxySettings{host=null, port=null, username=null, password=null}}, heartbeatSocketSettings=SocketSettings{connectTimeoutMS=10000, readTimeoutMS=10000, receiveBufferSize=0, proxySettings=ProxySettings{host=null, port=null, username=null, password=null}}, connectionPoolSettings=ConnectionPoolSettings{maxSize=100, minSize=0, maxWaitTimeMS=120000, maxConnectionLifeTimeMS=0, maxConnectionIdleTimeMS=0, maintenanceInitialDelayMS=0, maintenanceFrequencyMS=60000, connectionPoolListeners=[io.micrometer.core.instrument.binder.mongodb.MongoMetricsConnectionPoolListener@35a9fd22], maxConnecting=2}, serverSettings=ServerSettings{heartbeatFrequencyMS=10000, minHeartbeatFrequencyMS=500, serverMonitoringMode=AUTO, serverListeners='[]', serverMonitorListeners='[]'}, sslSettings=SslSettings{enabled=false, invalidHostNameAllowed=false, context=null}, applicationName='null', compressorList=[], uuidRepresentation=JAVA_LEGACY, serverApi=null, autoEncryptionSettings=null, dnsClient=null, inetAddressResolver=null, contextProvider=null, timeoutMS=null}
[2m2025-08-02 09:16:26.862[0;39m [32m INFO[0;39m [35m8258[0;39m [2m---[0;39m [2m[168.9.254:37017][0;39m [36morg.mongodb.driver.cluster              [0;39m [2m:[0;39m Monitor thread successfully connected to server with description ServerDescription{address=*************:37017, type=STANDALONE, cryptd=false, state=CONNECTED, ok=true, minWireVersion=0, maxWireVersion=13, maxDocumentSize=16777216, logicalSessionTimeoutMinutes=30, roundTripTimeNanos=87866333, minRoundTripTimeNanos=0}
[2m2025-08-02 09:16:27.091[0;39m [32m INFO[0;39m [35m8258[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.g.y.a.MybatisPlusJoinAutoConfiguration[0;39m [2m:[0;39m mybatis plus join properties config complete
[2m2025-08-02 09:16:27.414[0;39m [32m INFO[0;39m [35m8258[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.g.y.a.MybatisPlusJoinAutoConfiguration[0;39m [2m:[0;39m mybatis plus join SqlInjector init
[2m2025-08-02 09:16:27.420[0;39m [32mDEBUG[0;39m [35m8258[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Registered plugin: 'com.sanyth.core.handler.DecryptInterceptor@6010b36e'
[2m2025-08-02 09:16:27.420[0;39m [32mDEBUG[0;39m [35m8258[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Registered plugin: 'com.baomidou.mybatisplus.extension.plugins.MybatisPlusInterceptor@1097f455'
[2m2025-08-02 09:16:27.420[0;39m [32mDEBUG[0;39m [35m8258[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Registered plugin: 'com.github.yulichang.interceptor.MPJInterceptor@6a8648ea'
[2m2025-08-02 09:16:27.800[0;39m [32mDEBUG[0;39m [35m8258[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-dev/sanyth-dev-platform/sanyth-upms/target/classes/com/sanyth/upms/code/mapper/xml/CodeBjbMapper.xml]'
[2m2025-08-02 09:16:27.886[0;39m [32mDEBUG[0;39m [35m8258[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-dev/sanyth-dev-platform/sanyth-upms/target/classes/com/sanyth/upms/code/mapper/xml/CodeCommonMapper.xml]'
[2m2025-08-02 09:16:27.944[0;39m [32mDEBUG[0;39m [35m8258[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-dev/sanyth-dev-platform/sanyth-upms/target/classes/com/sanyth/upms/code/mapper/xml/CodeDwbMapper.xml]'
[2m2025-08-02 09:16:27.995[0;39m [32mDEBUG[0;39m [35m8258[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-dev/sanyth-dev-platform/sanyth-upms/target/classes/com/sanyth/upms/code/mapper/xml/CodeTypeMapper.xml]'
[2m2025-08-02 09:16:28.058[0;39m [32mDEBUG[0;39m [35m8258[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-dev/sanyth-dev-platform/sanyth-upms/target/classes/com/sanyth/upms/code/mapper/xml/CodeZybMapper.xml]'
[2m2025-08-02 09:16:28.103[0;39m [32mDEBUG[0;39m [35m8258[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-dev/sanyth-dev-platform/sanyth-upms/target/classes/com/sanyth/upms/system/mapper/xml/LoginRecordMapper.xml]'
[2m2025-08-02 09:16:28.135[0;39m [32mDEBUG[0;39m [35m8258[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-dev/sanyth-dev-platform/sanyth-upms/target/classes/com/sanyth/upms/system/mapper/xml/RoleMenuMapper.xml]'
[2m2025-08-02 09:16:28.169[0;39m [32mDEBUG[0;39m [35m8258[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-dev/sanyth-dev-platform/sanyth-upms/target/classes/com/sanyth/upms/system/mapper/xml/SysAccountMapper.xml]'
[2m2025-08-02 09:16:28.196[0;39m [32mDEBUG[0;39m [35m8258[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-dev/sanyth-dev-platform/sanyth-upms/target/classes/com/sanyth/upms/system/mapper/xml/SysAccountRoleMapper.xml]'
[2m2025-08-02 09:16:28.271[0;39m [32mDEBUG[0;39m [35m8258[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-dev/sanyth-dev-platform/sanyth-upms/target/classes/com/sanyth/upms/system/mapper/xml/SysMenuMapper.xml]'
[2m2025-08-02 09:16:28.299[0;39m [32mDEBUG[0;39m [35m8258[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-dev/sanyth-dev-platform/sanyth-upms/target/classes/com/sanyth/upms/system/mapper/xml/SysParamMapper.xml]'
[2m2025-08-02 09:16:28.322[0;39m [32mDEBUG[0;39m [35m8258[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-dev/sanyth-dev-platform/sanyth-upms/target/classes/com/sanyth/upms/system/mapper/xml/SysRoleMapper.xml]'
[2m2025-08-02 09:16:28.350[0;39m [32mDEBUG[0;39m [35m8258[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-dev/sanyth-dev-platform/sanyth-upms/target/classes/com/sanyth/upms/system/mapper/xml/SysRoleScopeMapper.xml]'
[2m2025-08-02 09:16:28.402[0;39m [32mDEBUG[0;39m [35m8258[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-dev/sanyth-dev-platform/sanyth-upms/target/classes/com/sanyth/upms/system/mapper/xml/UserInfoMapper.xml]'
[2m2025-08-02 09:16:28.414[0;39m [32mDEBUG[0;39m [35m8258[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.mybatisplus.core.toolkit.Sequence   [0;39m [2m:[0;39m Initialization Sequence datacenterId:0 workerId:13
[2m2025-08-02 09:16:28.510[0;39m [32m INFO[0;39m [35m8258[0;39m [2m---[0;39m [2m[           main][0;39m [36mcom.sanyth.core.config.MongoConfig      [0;39m [2m:[0;39m GridFSBucket 初始化成功 - 数据库: server, Bucket: fs, 块大小: 255KB
[2m2025-08-02 09:16:28.688[0;39m [32m INFO[0;39m [35m8258[0;39m [2m---[0;39m [2m[           main][0;39m [36morg.redisson.Version                    [0;39m [2m:[0;39m Redisson 3.36.0
[2m2025-08-02 09:16:28.803[0;39m [31mERROR[0;39m [35m8258[0;39m [2m---[0;39m [2m[           main][0;39m [36mi.n.r.d.DnsServerAddressStreamProviders [0;39m [2m:[0;39m Unable to load io.netty.resolver.dns.macos.MacOSDnsServerAddressStreamProvider, fallback to system defaults. This may result in incorrect DNS resolutions on MacOS. Check whether you have a dependency on 'io.netty:netty-resolver-dns-native-macos'. Use DEBUG level to see the full stack: java.lang.UnsatisfiedLinkError: failed to load the required native library
[2m2025-08-02 09:16:29.257[0;39m [32m INFO[0;39m [35m8258[0;39m [2m---[0;39m [2m[isson-netty-1-6][0;39m [36mo.redisson.connection.ConnectionsHolder [0;39m [2m:[0;39m 1 connections initialized for *************/*************:6380
[2m2025-08-02 09:16:32.243[0;39m [32m INFO[0;39m [35m8258[0;39m [2m---[0;39m [2m[sson-netty-1-19][0;39m [36mo.redisson.connection.ConnectionsHolder [0;39m [2m:[0;39m 24 connections initialized for *************/*************:6380
[2m2025-08-02 09:16:32.482[0;39m [32mDEBUG[0;39m [35m8258[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.s.a.security.JwtAuthenticationFilter  [0;39m [2m:[0;39m Filter 'jwtAuthenticationFilter' configured for use
[2m2025-08-02 09:16:32.700[0;39m [32m INFO[0;39m [35m8258[0;39m [2m---[0;39m [2m[           main][0;39m [36mo.hibernate.jpa.internal.util.LogHelper [0;39m [2m:[0;39m HHH000204: Processing PersistenceUnitInfo [name: default]
[2m2025-08-02 09:16:32.770[0;39m [32m INFO[0;39m [35m8258[0;39m [2m---[0;39m [2m[           main][0;39m [36morg.hibernate.Version                   [0;39m [2m:[0;39m HHH000412: Hibernate ORM core version 6.6.18.Final
[2m2025-08-02 09:16:32.845[0;39m [32m INFO[0;39m [35m8258[0;39m [2m---[0;39m [2m[           main][0;39m [36mo.h.c.internal.RegionFactoryInitiator   [0;39m [2m:[0;39m HHH000026: Second-level cache disabled
[2m2025-08-02 09:16:33.187[0;39m [32m INFO[0;39m [35m8258[0;39m [2m---[0;39m [2m[           main][0;39m [36mo.s.o.j.p.SpringPersistenceUnitInfo     [0;39m [2m:[0;39m No LoadTimeWeaver setup: ignoring JPA class transformer
[2m2025-08-02 09:16:33.348[0;39m [32m INFO[0;39m [35m8258[0;39m [2m---[0;39m [2m[           main][0;39m [36mcom.alibaba.druid.pool.DruidDataSource  [0;39m [2m:[0;39m {dataSource-1} inited
[2m2025-08-02 09:16:35.060[0;39m [33m WARN[0;39m [35m8258[0;39m [2m---[0;39m [2m[           main][0;39m [36morg.hibernate.orm.deprecation           [0;39m [2m:[0;39m HHH90000025: OracleDialect does not need to be specified explicitly using 'hibernate.dialect' (remove the property setting and it will be selected by default)
[2m2025-08-02 09:16:35.291[0;39m [32m INFO[0;39m [35m8258[0;39m [2m---[0;39m [2m[           main][0;39m [36morg.hibernate.orm.connections.pooling   [0;39m [2m:[0;39m HHH10001005: Database info:
	Database JDBC URL [Connecting through datasource '{
	CreateTime:"2025-08-02 09:16:27",
	ActiveCount:0,
	PoolingCount:1,
	CreateCount:1,
	DestroyCount:0,
	CloseCount:1,
	ConnectCount:1,
	Connections:[
		{ID:781431739, ConnectTime:"2025-08-02 09:16:34", UseCount:1, LastActiveTime:"2025-08-02 09:16:35"}
	]
}']
	Database driver: undefined/unknown
	Database version: 19.3
	Autocommit mode: undefined/unknown
	Isolation level: undefined/unknown
	Minimum pool size: undefined/unknown
	Maximum pool size: undefined/unknown
[2m2025-08-02 09:16:37.042[0;39m [32m INFO[0;39m [35m8258[0;39m [2m---[0;39m [2m[           main][0;39m [36mo.h.e.t.j.p.i.JtaPlatformInitiator      [0;39m [2m:[0;39m HHH000489: No JTA platform available (set 'hibernate.transaction.jta.platform' to enable JTA platform integration)
[2m2025-08-02 09:16:37.052[0;39m [32m INFO[0;39m [35m8258[0;39m [2m---[0;39m [2m[           main][0;39m [36mj.LocalContainerEntityManagerFactoryBean[0;39m [2m:[0;39m Initialized JPA EntityManagerFactory for persistence unit 'default'
[2m2025-08-02 09:16:38.365[0;39m [32mDEBUG[0;39m [35m8258[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.s.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m original SQL: SELECT    ID,TYPE,MODEL,PROVIDER,NAME,RESPONSE_LIMIT,TEMPERATURE,TOP_P,API_KEY,BASE_URL,SECRET_KEY,ENDPOINT,IMAGE_SIZE,IMAGE_QUALITY,IMAGE_STYLE,DIMENSIONS,STATUS    FROM  SYT_AIGC_MODEL
[2m2025-08-02 09:16:38.454[0;39m [32mDEBUG[0;39m [35m8258[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.s.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m SQL to parse, SQL: SELECT    ID,TYPE,MODEL,PROVIDER,NAME,RESPONSE_LIMIT,TEMPERATURE,TOP_P,API_KEY,BASE_URL,SECRET_KEY,ENDPOINT,IMAGE_SIZE,IMAGE_QUALITY,IMAGE_STYLE,DIMENSIONS,STATUS    FROM  SYT_AIGC_MODEL
[2m2025-08-02 09:16:38.461[0;39m [32mDEBUG[0;39m [35m8258[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.s.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m parse the finished SQL: SELECT ID, TYPE, MODEL, PROVIDER, NAME, RESPONSE_LIMIT, TEMPERATURE, TOP_P, API_KEY, BASE_URL, SECRET_KEY, ENDPOINT, IMAGE_SIZE, IMAGE_QUALITY, IMAGE_STYLE, DIMENSIONS, STATUS FROM SYT_AIGC_MODEL
[2m2025-08-02 09:16:38.478[0;39m [32mDEBUG[0;39m [35m8258[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.s.l.a.m.AigcModelMapper.selectList    [0;39m [2m:[0;39m ==>  Preparing: SELECT ID, TYPE, MODEL, PROVIDER, NAME, RESPONSE_LIMIT, TEMPERATURE, TOP_P, API_KEY, BASE_URL, SECRET_KEY, ENDPOINT, IMAGE_SIZE, IMAGE_QUALITY, IMAGE_STYLE, DIMENSIONS, STATUS FROM SYT_AIGC_MODEL
[2m2025-08-02 09:16:38.528[0;39m [32mDEBUG[0;39m [35m8258[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.s.l.a.m.AigcModelMapper.selectList    [0;39m [2m:[0;39m ==> Parameters: 
[2m2025-08-02 09:16:38.627[0;39m [32mDEBUG[0;39m [35m8258[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.s.l.a.m.AigcModelMapper.selectList    [0;39m [2m:[0;39m <==      Total: 4
[2m2025-08-02 09:16:38.967[0;39m [32m INFO[0;39m [35m8258[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.s.l.core.provider.ModelStoreFactory   [0;39m [2m:[0;39m 已成功注册模型：OLLAMA -- CHAT， 模型配置：AigcModel(id=b69a8765b69ccd2c59e646a926f55fb2, type=CHAT, model=qwen2.5, provider=OLLAMA, name=Ollama/qwen2.5, responseLimit=2033, temperature=0.2, topP=0.3, apiKey=null, baseUrl=http://localhost:11434/, secretKey=null, endpoint=null, imageSize=null, imageQuality=null, imageStyle=null, dimensions=null, status=enabled)
[2m2025-08-02 09:16:38.972[0;39m [32m INFO[0;39m [35m8258[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.s.l.core.provider.ModelStoreFactory   [0;39m [2m:[0;39m 已成功注册模型：OPENAI -- CHAT， 模型配置：AigcModel(id=eddd81d38381ff475f735e539b163e74, type=CHAT, model=Qwen/Qwen3-8B, provider=OPENAI, name=Qwen3-8B, responseLimit=2000, temperature=0.2, topP=0.8, apiKey=sk-heqiftpvnebkqetwsszbclmrneqhwuildjwuvujtdhkeiqnf, baseUrl=https://api.siliconflow.cn/v1, secretKey=null, endpoint=null, imageSize=null, imageQuality=null, imageStyle=null, dimensions=null, status=enabled)
[2m2025-08-02 09:16:38.972[0;39m [32m INFO[0;39m [35m8258[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.s.l.core.provider.ModelStoreFactory   [0;39m [2m:[0;39m 已成功注册模型：OPENAI -- EMBEDDING， 模型配置：AigcModel(id=097a00062ad6110d1c86e6fcc1391791, type=EMBEDDING, model=BAAI/bge-m3, provider=OPENAI, name=bge-m3, responseLimit=2000, temperature=0.2, topP=0.5, apiKey=sk-heqiftpvnebkqetwsszbclmrneqhwuildjwuvujtdhkeiqnf, baseUrl=https://api.siliconflow.cn/v1, secretKey=null, endpoint=null, imageSize=null, imageQuality=null, imageStyle=null, dimensions=null, status=enabled)
[2m2025-08-02 09:16:38.972[0;39m [32m INFO[0;39m [35m8258[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.s.l.core.provider.ModelStoreFactory   [0;39m [2m:[0;39m 已成功注册模型：OLLAMA -- EMBEDDING， 模型配置：AigcModel(id=618b4c8f87bca57686748264817b34dc, type=EMBEDDING, model=bge-m3, provider=OLLAMA, name=Ollama/bge-m3, responseLimit=2000, temperature=0.2, topP=0.8, apiKey=null, baseUrl=http://localhost:11434/, secretKey=null, endpoint=null, imageSize=null, imageQuality=null, imageStyle=null, dimensions=null, status=enabled)
[2m2025-08-02 09:16:39.061[0;39m [32mDEBUG[0;39m [35m8258[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.s.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m original SQL: SELECT    ID,NAME,PROVIDER,HOST,USERNAME,DIMENSION,DATABASE_NAME,PORT,TABLE_NAME,PASSWORD    FROM  SYT_AIGC_EMBED_STORE
[2m2025-08-02 09:16:39.067[0;39m [32mDEBUG[0;39m [35m8258[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.s.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m SQL to parse, SQL: SELECT    ID,NAME,PROVIDER,HOST,USERNAME,DIMENSION,DATABASE_NAME,PORT,TABLE_NAME,PASSWORD    FROM  SYT_AIGC_EMBED_STORE
[2m2025-08-02 09:16:39.068[0;39m [32mDEBUG[0;39m [35m8258[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.s.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m parse the finished SQL: SELECT ID, NAME, PROVIDER, HOST, USERNAME, DIMENSION, DATABASE_NAME, PORT, TABLE_NAME, PASSWORD FROM SYT_AIGC_EMBED_STORE
[2m2025-08-02 09:16:39.068[0;39m [32mDEBUG[0;39m [35m8258[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.s.l.a.m.A.selectList                  [0;39m [2m:[0;39m ==>  Preparing: SELECT ID, NAME, PROVIDER, HOST, USERNAME, DIMENSION, DATABASE_NAME, PORT, TABLE_NAME, PASSWORD FROM SYT_AIGC_EMBED_STORE
[2m2025-08-02 09:16:39.069[0;39m [32mDEBUG[0;39m [35m8258[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.s.l.a.m.A.selectList                  [0;39m [2m:[0;39m ==> Parameters: 
[2m2025-08-02 09:16:39.174[0;39m [32mDEBUG[0;39m [35m8258[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.s.l.a.m.A.selectList                  [0;39m [2m:[0;39m <==      Total: 1
[2m2025-08-02 09:16:40.197[0;39m [32m INFO[0;39m [35m8258[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.s.l.c.provider.EmbeddingStoreFactory  [0;39m [2m:[0;39m 已成功注册Embedding Store：PGVECTOR， 配置信息：AigcEmbedStore(id=8eead31ed55dadfd634f6118cad0e5c7, name=Pgvector, provider=PGVECTOR, host=*************, username=root, dimension=1024, databaseName=langchain, port=5432, tableName=langchain_store, password=langchain123456)
[2m2025-08-02 09:16:40.370[0;39m [32mDEBUG[0;39m [35m8258[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.s.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m original SQL: SELECT    ID,NAME,EMBED_STORE_ID,EMBED_MODEL_ID,COVER,DES,CREATE_TIME,MAX_SEGMENT_SIZE,MAX_OVERLAP_SIZE    FROM  SYT_AIGC_KNOWLEDGE
[2m2025-08-02 09:16:40.375[0;39m [32mDEBUG[0;39m [35m8258[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.s.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m SQL to parse, SQL: SELECT    ID,NAME,EMBED_STORE_ID,EMBED_MODEL_ID,COVER,DES,CREATE_TIME,MAX_SEGMENT_SIZE,MAX_OVERLAP_SIZE    FROM  SYT_AIGC_KNOWLEDGE
[2m2025-08-02 09:16:40.376[0;39m [32mDEBUG[0;39m [35m8258[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.s.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m parse the finished SQL: SELECT ID, NAME, EMBED_STORE_ID, EMBED_MODEL_ID, COVER, DES, CREATE_TIME, MAX_SEGMENT_SIZE, MAX_OVERLAP_SIZE FROM SYT_AIGC_KNOWLEDGE
[2m2025-08-02 09:16:40.377[0;39m [32mDEBUG[0;39m [35m8258[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.s.l.a.m.A.selectList                  [0;39m [2m:[0;39m ==>  Preparing: SELECT ID, NAME, EMBED_STORE_ID, EMBED_MODEL_ID, COVER, DES, CREATE_TIME, MAX_SEGMENT_SIZE, MAX_OVERLAP_SIZE FROM SYT_AIGC_KNOWLEDGE
[2m2025-08-02 09:16:40.377[0;39m [32mDEBUG[0;39m [35m8258[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.s.l.a.m.A.selectList                  [0;39m [2m:[0;39m ==> Parameters: 
[2m2025-08-02 09:16:40.459[0;39m [32mDEBUG[0;39m [35m8258[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.s.l.a.m.A.selectList                  [0;39m [2m:[0;39m <==      Total: 7
[2m2025-08-02 09:16:40.459[0;39m [32mDEBUG[0;39m [35m8258[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.s.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m original SQL: SELECT    ID,TYPE,MODEL,PROVIDER,NAME,RESPONSE_LIMIT,TEMPERATURE,TOP_P,API_KEY,BASE_URL,SECRET_KEY,ENDPOINT,IMAGE_SIZE,IMAGE_QUALITY,IMAGE_STYLE,DIMENSIONS,STATUS    FROM  SYT_AIGC_MODEL
[2m2025-08-02 09:16:40.467[0;39m [32mDEBUG[0;39m [35m8258[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.s.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m SQL to parse, SQL: SELECT    ID,TYPE,MODEL,PROVIDER,NAME,RESPONSE_LIMIT,TEMPERATURE,TOP_P,API_KEY,BASE_URL,SECRET_KEY,ENDPOINT,IMAGE_SIZE,IMAGE_QUALITY,IMAGE_STYLE,DIMENSIONS,STATUS    FROM  SYT_AIGC_MODEL
[2m2025-08-02 09:16:40.469[0;39m [32mDEBUG[0;39m [35m8258[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.s.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m parse the finished SQL: SELECT ID, TYPE, MODEL, PROVIDER, NAME, RESPONSE_LIMIT, TEMPERATURE, TOP_P, API_KEY, BASE_URL, SECRET_KEY, ENDPOINT, IMAGE_SIZE, IMAGE_QUALITY, IMAGE_STYLE, DIMENSIONS, STATUS FROM SYT_AIGC_MODEL
[2m2025-08-02 09:16:40.469[0;39m [32mDEBUG[0;39m [35m8258[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.s.l.a.m.AigcModelMapper.selectList    [0;39m [2m:[0;39m ==>  Preparing: SELECT ID, TYPE, MODEL, PROVIDER, NAME, RESPONSE_LIMIT, TEMPERATURE, TOP_P, API_KEY, BASE_URL, SECRET_KEY, ENDPOINT, IMAGE_SIZE, IMAGE_QUALITY, IMAGE_STYLE, DIMENSIONS, STATUS FROM SYT_AIGC_MODEL
[2m2025-08-02 09:16:40.469[0;39m [32mDEBUG[0;39m [35m8258[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.s.l.a.m.AigcModelMapper.selectList    [0;39m [2m:[0;39m ==> Parameters: 
[2m2025-08-02 09:16:40.528[0;39m [32mDEBUG[0;39m [35m8258[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.s.l.a.m.AigcModelMapper.selectList    [0;39m [2m:[0;39m <==      Total: 4
[2m2025-08-02 09:16:40.530[0;39m [32mDEBUG[0;39m [35m8258[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.s.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m original SQL: SELECT    ID,NAME,PROVIDER,HOST,USERNAME,DIMENSION,DATABASE_NAME,PORT,TABLE_NAME,PASSWORD    FROM  SYT_AIGC_EMBED_STORE
[2m2025-08-02 09:16:40.534[0;39m [32mDEBUG[0;39m [35m8258[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.s.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m SQL to parse, SQL: SELECT    ID,NAME,PROVIDER,HOST,USERNAME,DIMENSION,DATABASE_NAME,PORT,TABLE_NAME,PASSWORD    FROM  SYT_AIGC_EMBED_STORE
[2m2025-08-02 09:16:40.536[0;39m [32mDEBUG[0;39m [35m8258[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.s.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m parse the finished SQL: SELECT ID, NAME, PROVIDER, HOST, USERNAME, DIMENSION, DATABASE_NAME, PORT, TABLE_NAME, PASSWORD FROM SYT_AIGC_EMBED_STORE
[2m2025-08-02 09:16:40.536[0;39m [32mDEBUG[0;39m [35m8258[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.s.l.a.m.A.selectList                  [0;39m [2m:[0;39m ==>  Preparing: SELECT ID, NAME, PROVIDER, HOST, USERNAME, DIMENSION, DATABASE_NAME, PORT, TABLE_NAME, PASSWORD FROM SYT_AIGC_EMBED_STORE
[2m2025-08-02 09:16:40.536[0;39m [32mDEBUG[0;39m [35m8258[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.s.l.a.m.A.selectList                  [0;39m [2m:[0;39m ==> Parameters: 
[2m2025-08-02 09:16:40.592[0;39m [32mDEBUG[0;39m [35m8258[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.s.l.a.m.A.selectList                  [0;39m [2m:[0;39m <==      Total: 1
[2m2025-08-02 09:16:40.844[0;39m [32m INFO[0;39m [35m8258[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.s.langchain.api.store.AppChannelStore [0;39m [2m:[0;39m 初始化应用程序频道配置列表...
[2m2025-08-02 09:16:40.844[0;39m [32mDEBUG[0;39m [35m8258[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.s.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m original SQL: SELECT    ID,APP_ID,API_KEY,CREATE_TIME,CHANNEL    FROM  SYT_AIGC_APP_API
[2m2025-08-02 09:16:40.848[0;39m [32mDEBUG[0;39m [35m8258[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.s.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m SQL to parse, SQL: SELECT    ID,APP_ID,API_KEY,CREATE_TIME,CHANNEL    FROM  SYT_AIGC_APP_API
[2m2025-08-02 09:16:40.850[0;39m [32mDEBUG[0;39m [35m8258[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.s.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m parse the finished SQL: SELECT ID, APP_ID, API_KEY, CREATE_TIME, CHANNEL FROM SYT_AIGC_APP_API
[2m2025-08-02 09:16:40.850[0;39m [32mDEBUG[0;39m [35m8258[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.s.l.a.m.AigcAppApiMapper.selectList   [0;39m [2m:[0;39m ==>  Preparing: SELECT ID, APP_ID, API_KEY, CREATE_TIME, CHANNEL FROM SYT_AIGC_APP_API
[2m2025-08-02 09:16:40.850[0;39m [32mDEBUG[0;39m [35m8258[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.s.l.a.m.AigcAppApiMapper.selectList   [0;39m [2m:[0;39m ==> Parameters: 
[2m2025-08-02 09:16:40.913[0;39m [32mDEBUG[0;39m [35m8258[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.s.l.a.m.AigcAppApiMapper.selectList   [0;39m [2m:[0;39m <==      Total: 1
[2m2025-08-02 09:16:40.914[0;39m [32m INFO[0;39m [35m8258[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.s.langchain.api.store.AppChannelStore [0;39m [2m:[0;39m 成功初始化 1 API通道
[2m2025-08-02 09:16:40.973[0;39m [32m INFO[0;39m [35m8258[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.sanyth.langchain.api.store.AppStore   [0;39m [2m:[0;39m Initializing app config list...
[2m2025-08-02 09:16:40.973[0;39m [32mDEBUG[0;39m [35m8258[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.s.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m original SQL: SELECT    ID,CLASS_ID,DES,PROMPT,NAME,CREATE_TIME,SAVE_TIME,COVER,KNOWLEDGE_IDS,TOOL_IDS,MCP_SERVER_ID,MODEL_ID,PROLOGUE,STATUS,MAX_RESULTS,MIN_SCORE,PRESET_QUESTION    FROM  SYT_AIGC_APP
[2m2025-08-02 09:16:40.980[0;39m [32mDEBUG[0;39m [35m8258[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.s.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m SQL to parse, SQL: SELECT    ID,CLASS_ID,DES,PROMPT,NAME,CREATE_TIME,SAVE_TIME,COVER,KNOWLEDGE_IDS,TOOL_IDS,MCP_SERVER_ID,MODEL_ID,PROLOGUE,STATUS,MAX_RESULTS,MIN_SCORE,PRESET_QUESTION    FROM  SYT_AIGC_APP
[2m2025-08-02 09:16:40.981[0;39m [32mDEBUG[0;39m [35m8258[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.s.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m parse the finished SQL: SELECT ID, CLASS_ID, DES, PROMPT, NAME, CREATE_TIME, SAVE_TIME, COVER, KNOWLEDGE_IDS, TOOL_IDS, MCP_SERVER_ID, MODEL_ID, PROLOGUE, STATUS, MAX_RESULTS, MIN_SCORE, PRESET_QUESTION FROM SYT_AIGC_APP
[2m2025-08-02 09:16:40.981[0;39m [32mDEBUG[0;39m [35m8258[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.s.l.a.mapper.AigcAppMapper.selectList [0;39m [2m:[0;39m ==>  Preparing: SELECT ID, CLASS_ID, DES, PROMPT, NAME, CREATE_TIME, SAVE_TIME, COVER, KNOWLEDGE_IDS, TOOL_IDS, MCP_SERVER_ID, MODEL_ID, PROLOGUE, STATUS, MAX_RESULTS, MIN_SCORE, PRESET_QUESTION FROM SYT_AIGC_APP
[2m2025-08-02 09:16:40.982[0;39m [32mDEBUG[0;39m [35m8258[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.s.l.a.mapper.AigcAppMapper.selectList [0;39m [2m:[0;39m ==> Parameters: 
[2m2025-08-02 09:16:41.284[0;39m [32mDEBUG[0;39m [35m8258[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.s.l.a.mapper.AigcAppMapper.selectList [0;39m [2m:[0;39m <==      Total: 3
[2m2025-08-02 09:16:41.285[0;39m [32m INFO[0;39m [35m8258[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.sanyth.langchain.api.store.AppStore   [0;39m [2m:[0;39m Successfully initialized 3 apps
[2m2025-08-02 09:16:42.164[0;39m [33m WARN[0;39m [35m8258[0;39m [2m---[0;39m [2m[           main][0;39m [36mJpaBaseConfiguration$JpaWebConfiguration[0;39m [2m:[0;39m spring.jpa.open-in-view is enabled by default. Therefore, database queries may be performed during view rendering. Explicitly configure spring.jpa.open-in-view to disable this warning
[2m2025-08-02 09:16:42.690[0;39m [32m INFO[0;39m [35m8258[0;39m [2m---[0;39m [2m[           main][0;39m [36mo.s.b.a.e.web.EndpointLinksResolver     [0;39m [2m:[0;39m Exposing 1 endpoint beneath base path '/actuator'
[2m2025-08-02 09:16:42.739[0;39m [33m WARN[0;39m [35m8258[0;39m [2m---[0;39m [2m[           main][0;39m [36m.s.s.UserDetailsServiceAutoConfiguration[0;39m [2m:[0;39m 

Using generated security password: 789131f1-5d43-4877-bdba-96aa4e926fb1

This generated password is for development use only. Your security configuration must be updated before running your application in production.

[2m2025-08-02 09:16:42.751[0;39m [32m INFO[0;39m [35m8258[0;39m [2m---[0;39m [2m[           main][0;39m [36mr$InitializeUserDetailsManagerConfigurer[0;39m [2m:[0;39m Global AuthenticationManager configured with UserDetailsService bean with name inMemoryUserDetailsManager
[2m2025-08-02 09:16:43.224[0;39m [32m INFO[0;39m [35m8258[0;39m [2m---[0;39m [2m[           main][0;39m [36mo.s.b.d.a.OptionalLiveReloadServer      [0;39m [2m:[0;39m LiveReload server is running on port 35729
[2m2025-08-02 09:16:43.594[0;39m [32m INFO[0;39m [35m8258[0;39m [2m---[0;39m [2m[           main][0;39m [36mctiveUserDetailsServiceAutoConfiguration[0;39m [2m:[0;39m 

Using generated security password: 789131f1-5d43-4877-bdba-96aa4e926fb1

[2m2025-08-02 09:16:43.788[0;39m [32m INFO[0;39m [35m8258[0;39m [2m---[0;39m [2m[           main][0;39m [36mo.a.coyote.http11.Http11NioProtocol     [0;39m [2m:[0;39m Starting ProtocolHandler ["http-nio-9096"]
[2m2025-08-02 09:16:43.838[0;39m [32m INFO[0;39m [35m8258[0;39m [2m---[0;39m [2m[           main][0;39m [36mo.s.b.w.embedded.tomcat.TomcatWebServer [0;39m [2m:[0;39m Tomcat started on port 9096 (http) with context path '/'
[2m2025-08-02 09:16:43.855[0;39m [32m INFO[0;39m [35m8258[0;39m [2m---[0;39m [2m[           main][0;39m [36mcom.sanyth.App                          [0;39m [2m:[0;39m Started App in 24.755 seconds (process running for 26.956)
[2m2025-08-02 09:16:43.870[0;39m [32mDEBUG[0;39m [35m8258[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.a.DdlApplicationRunner            [0;39m [2m:[0;39m   ...  DDL start create  ...  
[2m2025-08-02 09:16:43.871[0;39m [32mDEBUG[0;39m [35m8258[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.a.DdlApplicationRunner            [0;39m [2m:[0;39m   ...  DDL end create  ...  
[2m2025-08-02 09:18:26.941[0;39m [32m INFO[0;39m [35m8258[0;39m [2m---[0;39m [2m[nio-9096-exec-1][0;39m [36mo.a.c.c.C.[Tomcat].[localhost].[/]      [0;39m [2m:[0;39m Initializing Spring DispatcherServlet 'dispatcherServlet'
[2m2025-08-02 09:18:26.942[0;39m [32m INFO[0;39m [35m8258[0;39m [2m---[0;39m [2m[nio-9096-exec-1][0;39m [36mo.s.web.servlet.DispatcherServlet       [0;39m [2m:[0;39m Initializing Servlet 'dispatcherServlet'
[2m2025-08-02 09:18:26.949[0;39m [32m INFO[0;39m [35m8258[0;39m [2m---[0;39m [2m[nio-9096-exec-1][0;39m [36mo.s.web.servlet.DispatcherServlet       [0;39m [2m:[0;39m Completed initialization in 7 ms
[2m2025-08-02 09:18:30.283[0;39m [32mDEBUG[0;39m [35m8258[0;39m [2m---[0;39m [2m[nio-9096-exec-2][0;39m [36mc.s.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m original SQL: SELECT    ID,ACCOUNT_NON_EXPIRED,ACCOUNT_NON_LOCKED,CREDENTIALS_NON_EXPIRED,ENABLED,PASSWORD,USERNAME,REAL_NAME,GENDER,ACTIVE_FLAG,ID_TYPE,ID_CODE,DEPT_NAME,TEL_MOBILE,EMAIL,START_TIME,END_TIME,PASSWORD_LAST_UPDATE_TIME    FROM  SYT_SYS_ACCOUNT         WHERE  (username = ? OR id_code = ? OR tel_mobile = ?)
[2m2025-08-02 09:18:30.295[0;39m [32mDEBUG[0;39m [35m8258[0;39m [2m---[0;39m [2m[nio-9096-exec-2][0;39m [36mc.s.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m SQL to parse, SQL: SELECT    ID,ACCOUNT_NON_EXPIRED,ACCOUNT_NON_LOCKED,CREDENTIALS_NON_EXPIRED,ENABLED,PASSWORD,USERNAME,REAL_NAME,GENDER,ACTIVE_FLAG,ID_TYPE,ID_CODE,DEPT_NAME,TEL_MOBILE,EMAIL,START_TIME,END_TIME,PASSWORD_LAST_UPDATE_TIME    FROM  SYT_SYS_ACCOUNT         WHERE  (username = ? OR id_code = ? OR tel_mobile = ?)
[2m2025-08-02 09:18:30.297[0;39m [32mDEBUG[0;39m [35m8258[0;39m [2m---[0;39m [2m[nio-9096-exec-2][0;39m [36mc.s.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m parse the finished SQL: SELECT ID, ACCOUNT_NON_EXPIRED, ACCOUNT_NON_LOCKED, CREDENTIALS_NON_EXPIRED, ENABLED, PASSWORD, USERNAME, REAL_NAME, GENDER, ACTIVE_FLAG, ID_TYPE, ID_CODE, DEPT_NAME, TEL_MOBILE, EMAIL, START_TIME, END_TIME, PASSWORD_LAST_UPDATE_TIME FROM SYT_SYS_ACCOUNT WHERE (username = ? OR id_code = ? OR tel_mobile = ?)
[2m2025-08-02 09:18:30.359[0;39m [32mDEBUG[0;39m [35m8258[0;39m [2m---[0;39m [2m[nio-9096-exec-2][0;39m [36mc.s.u.s.m.SysAccountMapper.selectList   [0;39m [2m:[0;39m ==>  Preparing: SELECT ID, ACCOUNT_NON_EXPIRED, ACCOUNT_NON_LOCKED, CREDENTIALS_NON_EXPIRED, ENABLED, PASSWORD, USERNAME, REAL_NAME, GENDER, ACTIVE_FLAG, ID_TYPE, ID_CODE, DEPT_NAME, TEL_MOBILE, EMAIL, START_TIME, END_TIME, PASSWORD_LAST_UPDATE_TIME FROM SYT_SYS_ACCOUNT WHERE (username = ? OR id_code = ? OR tel_mobile = ?)
[2m2025-08-02 09:18:30.363[0;39m [32mDEBUG[0;39m [35m8258[0;39m [2m---[0;39m [2m[nio-9096-exec-2][0;39m [36mc.s.u.s.m.SysAccountMapper.selectList   [0;39m [2m:[0;39m ==> Parameters: admin(String), admin(String), admin(String)
[2m2025-08-02 09:18:30.454[0;39m [32mDEBUG[0;39m [35m8258[0;39m [2m---[0;39m [2m[nio-9096-exec-2][0;39m [36mc.s.u.s.m.SysAccountMapper.selectList   [0;39m [2m:[0;39m <==      Total: 1
[2m2025-08-02 09:18:30.455[0;39m [32mDEBUG[0;39m [35m8258[0;39m [2m---[0;39m [2m[nio-9096-exec-2][0;39m [36mc.s.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m original SQL: SELECT *
        FROM SYT_SYS_ROLE
        WHERE id IN (
            SELECT role_id
            FROM SYT_SYS_ACCOUNT_ROLE
            WHERE account_id = ?
        )
[2m2025-08-02 09:18:30.466[0;39m [32mDEBUG[0;39m [35m8258[0;39m [2m---[0;39m [2m[nio-9096-exec-2][0;39m [36mc.s.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m SQL to parse, SQL: SELECT *
        FROM SYT_SYS_ROLE
        WHERE id IN (
            SELECT role_id
            FROM SYT_SYS_ACCOUNT_ROLE
            WHERE account_id = ?
        )
[2m2025-08-02 09:18:30.468[0;39m [32mDEBUG[0;39m [35m8258[0;39m [2m---[0;39m [2m[nio-9096-exec-2][0;39m [36mc.s.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m parse the finished SQL: SELECT * FROM SYT_SYS_ROLE WHERE id IN (SELECT role_id FROM SYT_SYS_ACCOUNT_ROLE WHERE account_id = ?)
[2m2025-08-02 09:18:30.469[0;39m [32mDEBUG[0;39m [35m8258[0;39m [2m---[0;39m [2m[nio-9096-exec-2][0;39m [36mc.s.u.s.m.S.selectByAccountId           [0;39m [2m:[0;39m ==>  Preparing: SELECT * FROM SYT_SYS_ROLE WHERE id IN (SELECT role_id FROM SYT_SYS_ACCOUNT_ROLE WHERE account_id = ?)
[2m2025-08-02 09:18:30.469[0;39m [32mDEBUG[0;39m [35m8258[0;39m [2m---[0;39m [2m[nio-9096-exec-2][0;39m [36mc.s.u.s.m.S.selectByAccountId           [0;39m [2m:[0;39m ==> Parameters: 13FF28E5AFA4D417E0630100007FE3FC(String)
[2m2025-08-02 09:18:30.547[0;39m [32mDEBUG[0;39m [35m8258[0;39m [2m---[0;39m [2m[nio-9096-exec-2][0;39m [36mc.s.u.s.m.S.selectByAccountId           [0;39m [2m:[0;39m <==      Total: 2
[2m2025-08-02 09:18:30.549[0;39m [32mDEBUG[0;39m [35m8258[0;39m [2m---[0;39m [2m[nio-9096-exec-2][0;39m [36mc.s.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m original SQL: SELECT a.*
        FROM SYT_SYS_MENU a
         WHERE  a.menu_id IN (
            SELECT menu_id FROM SYT_SYS_ROLE_MENU WHERE role_id IN (
            SELECT ta.role_id FROM SYT_SYS_ACCOUNT_ROLE ta LEFT JOIN syt_sys_role tb ON ta.role_id = tb.id
            WHERE ta.account_id = ? and ta.role_id = ?
            )
            )
            
            AND a.deleted = 0 
        ORDER BY a.sort_number
[2m2025-08-02 09:18:30.568[0;39m [32mDEBUG[0;39m [35m8258[0;39m [2m---[0;39m [2m[nio-9096-exec-2][0;39m [36mc.s.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m SQL to parse, SQL: SELECT a.*
        FROM SYT_SYS_MENU a
         WHERE  a.menu_id IN (
            SELECT menu_id FROM SYT_SYS_ROLE_MENU WHERE role_id IN (
            SELECT ta.role_id FROM SYT_SYS_ACCOUNT_ROLE ta LEFT JOIN syt_sys_role tb ON ta.role_id = tb.id
            WHERE ta.account_id = ? and ta.role_id = ?
            )
            )
            
            AND a.deleted = 0 
        ORDER BY a.sort_number
[2m2025-08-02 09:18:30.570[0;39m [32mDEBUG[0;39m [35m8258[0;39m [2m---[0;39m [2m[nio-9096-exec-2][0;39m [36mc.s.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m parse the finished SQL: SELECT a.* FROM SYT_SYS_MENU a WHERE a.menu_id IN (SELECT menu_id FROM SYT_SYS_ROLE_MENU WHERE role_id IN (SELECT ta.role_id FROM SYT_SYS_ACCOUNT_ROLE ta LEFT JOIN syt_sys_role tb ON ta.role_id = tb.id WHERE ta.account_id = ? AND ta.role_id = ?)) AND a.deleted = 0 ORDER BY a.sort_number
[2m2025-08-02 09:18:30.570[0;39m [32mDEBUG[0;39m [35m8258[0;39m [2m---[0;39m [2m[nio-9096-exec-2][0;39m [36mc.s.u.s.m.R.listMenuByAccount           [0;39m [2m:[0;39m ==>  Preparing: SELECT a.* FROM SYT_SYS_MENU a WHERE a.menu_id IN (SELECT menu_id FROM SYT_SYS_ROLE_MENU WHERE role_id IN (SELECT ta.role_id FROM SYT_SYS_ACCOUNT_ROLE ta LEFT JOIN syt_sys_role tb ON ta.role_id = tb.id WHERE ta.account_id = ? AND ta.role_id = ?)) AND a.deleted = 0 ORDER BY a.sort_number
[2m2025-08-02 09:18:30.571[0;39m [32mDEBUG[0;39m [35m8258[0;39m [2m---[0;39m [2m[nio-9096-exec-2][0;39m [36mc.s.u.s.m.R.listMenuByAccount           [0;39m [2m:[0;39m ==> Parameters: 13FF28E5AFA4D417E0630100007FE3FC(String), 36d5ca2b3906a97f4274ffb8e8a36210(String)
[2m2025-08-02 09:18:30.982[0;39m [32mDEBUG[0;39m [35m8258[0;39m [2m---[0;39m [2m[nio-9096-exec-2][0;39m [36mc.s.u.s.m.R.listMenuByAccount           [0;39m [2m:[0;39m <==      Total: 80
[2m2025-08-02 09:18:31.290[0;39m [32mDEBUG[0;39m [35m8258[0;39m [2m---[0;39m [2m[nio-9096-exec-2][0;39m [36mc.s.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m original SQL: SELECT XGH,BZ1,BZ2,BZ3,BZ4,BZ5,BZ6,BZ7,BZ8,BZ9,BZ10,BZ11,BZ12,BZ13,BZ14,BZ15,BZ16,BZ17,BZ18,BZ19,BZ20,BZ21,BZ22,BZ23,BZ24,BZ25,BZ26,BZ27,BZ28,BZ29,BZ30,BZ31,BZ32,BZ33,BZ34,BZ35,BZ36,BZ37,BZ38,BZ39,BZ40,BZ41,BZ42,BZ43,BZ44,BZ45,BZ46,BZ47,BZ48,BZ49,BZ50,BZ51,BZ52,BZ53,BZ54,BZ55,BZ56,BZ57,BZ58,BZ59,BZ60,XM,XB,XYID,ZYID,BJID,NJID,SJH,CSRQ,JG,MZMC,PYCCID,ZZMMMC,XQMC,ZJHM,ZJLX,XSLB,XZLX,RYZTID,ROLE_ID,USER_TYPE,PHOTO,deleted FROM SYT_USER_INFO WHERE XGH=? AND deleted=0
[2m2025-08-02 09:18:31.306[0;39m [32mDEBUG[0;39m [35m8258[0;39m [2m---[0;39m [2m[nio-9096-exec-2][0;39m [36mc.s.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m SQL to parse, SQL: SELECT XGH,BZ1,BZ2,BZ3,BZ4,BZ5,BZ6,BZ7,BZ8,BZ9,BZ10,BZ11,BZ12,BZ13,BZ14,BZ15,BZ16,BZ17,BZ18,BZ19,BZ20,BZ21,BZ22,BZ23,BZ24,BZ25,BZ26,BZ27,BZ28,BZ29,BZ30,BZ31,BZ32,BZ33,BZ34,BZ35,BZ36,BZ37,BZ38,BZ39,BZ40,BZ41,BZ42,BZ43,BZ44,BZ45,BZ46,BZ47,BZ48,BZ49,BZ50,BZ51,BZ52,BZ53,BZ54,BZ55,BZ56,BZ57,BZ58,BZ59,BZ60,XM,XB,XYID,ZYID,BJID,NJID,SJH,CSRQ,JG,MZMC,PYCCID,ZZMMMC,XQMC,ZJHM,ZJLX,XSLB,XZLX,RYZTID,ROLE_ID,USER_TYPE,PHOTO,deleted FROM SYT_USER_INFO WHERE XGH=? AND deleted=0
[2m2025-08-02 09:18:31.308[0;39m [32mDEBUG[0;39m [35m8258[0;39m [2m---[0;39m [2m[nio-9096-exec-2][0;39m [36mc.s.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m parse the finished SQL: SELECT XGH, BZ1, BZ2, BZ3, BZ4, BZ5, BZ6, BZ7, BZ8, BZ9, BZ10, BZ11, BZ12, BZ13, BZ14, BZ15, BZ16, BZ17, BZ18, BZ19, BZ20, BZ21, BZ22, BZ23, BZ24, BZ25, BZ26, BZ27, BZ28, BZ29, BZ30, BZ31, BZ32, BZ33, BZ34, BZ35, BZ36, BZ37, BZ38, BZ39, BZ40, BZ41, BZ42, BZ43, BZ44, BZ45, BZ46, BZ47, BZ48, BZ49, BZ50, BZ51, BZ52, BZ53, BZ54, BZ55, BZ56, BZ57, BZ58, BZ59, BZ60, XM, XB, XYID, ZYID, BJID, NJID, SJH, CSRQ, JG, MZMC, PYCCID, ZZMMMC, XQMC, ZJHM, ZJLX, XSLB, XZLX, RYZTID, ROLE_ID, USER_TYPE, PHOTO, deleted FROM SYT_USER_INFO WHERE XGH = ? AND deleted = 0
[2m2025-08-02 09:18:31.308[0;39m [32mDEBUG[0;39m [35m8258[0;39m [2m---[0;39m [2m[nio-9096-exec-2][0;39m [36mc.s.u.s.m.UserInfoMapper.selectById     [0;39m [2m:[0;39m ==>  Preparing: SELECT XGH, BZ1, BZ2, BZ3, BZ4, BZ5, BZ6, BZ7, BZ8, BZ9, BZ10, BZ11, BZ12, BZ13, BZ14, BZ15, BZ16, BZ17, BZ18, BZ19, BZ20, BZ21, BZ22, BZ23, BZ24, BZ25, BZ26, BZ27, BZ28, BZ29, BZ30, BZ31, BZ32, BZ33, BZ34, BZ35, BZ36, BZ37, BZ38, BZ39, BZ40, BZ41, BZ42, BZ43, BZ44, BZ45, BZ46, BZ47, BZ48, BZ49, BZ50, BZ51, BZ52, BZ53, BZ54, BZ55, BZ56, BZ57, BZ58, BZ59, BZ60, XM, XB, XYID, ZYID, BJID, NJID, SJH, CSRQ, JG, MZMC, PYCCID, ZZMMMC, XQMC, ZJHM, ZJLX, XSLB, XZLX, RYZTID, ROLE_ID, USER_TYPE, PHOTO, deleted FROM SYT_USER_INFO WHERE XGH = ? AND deleted = 0
[2m2025-08-02 09:18:31.309[0;39m [32mDEBUG[0;39m [35m8258[0;39m [2m---[0;39m [2m[nio-9096-exec-2][0;39m [36mc.s.u.s.m.UserInfoMapper.selectById     [0;39m [2m:[0;39m ==> Parameters: admin(String)
[2m2025-08-02 09:18:31.427[0;39m [32mDEBUG[0;39m [35m8258[0;39m [2m---[0;39m [2m[nio-9096-exec-2][0;39m [36mc.s.u.s.m.UserInfoMapper.selectById     [0;39m [2m:[0;39m <==      Total: 1
[2m2025-08-02 09:18:31.970[0;39m [32mDEBUG[0;39m [35m8258[0;39m [2m---[0;39m [2m[nio-9096-exec-5][0;39m [36mc.s.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m original SQL: SELECT XGH,BZ1,BZ2,BZ3,BZ4,BZ5,BZ6,BZ7,BZ8,BZ9,BZ10,BZ11,BZ12,BZ13,BZ14,BZ15,BZ16,BZ17,BZ18,BZ19,BZ20,BZ21,BZ22,BZ23,BZ24,BZ25,BZ26,BZ27,BZ28,BZ29,BZ30,BZ31,BZ32,BZ33,BZ34,BZ35,BZ36,BZ37,BZ38,BZ39,BZ40,BZ41,BZ42,BZ43,BZ44,BZ45,BZ46,BZ47,BZ48,BZ49,BZ50,BZ51,BZ52,BZ53,BZ54,BZ55,BZ56,BZ57,BZ58,BZ59,BZ60,XM,XB,XYID,ZYID,BJID,NJID,SJH,CSRQ,JG,MZMC,PYCCID,ZZMMMC,XQMC,ZJHM,ZJLX,XSLB,XZLX,RYZTID,ROLE_ID,USER_TYPE,PHOTO,deleted FROM SYT_USER_INFO WHERE XGH=? AND deleted=0
[2m2025-08-02 09:18:31.987[0;39m [32mDEBUG[0;39m [35m8258[0;39m [2m---[0;39m [2m[nio-9096-exec-5][0;39m [36mc.s.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m SQL to parse, SQL: SELECT XGH,BZ1,BZ2,BZ3,BZ4,BZ5,BZ6,BZ7,BZ8,BZ9,BZ10,BZ11,BZ12,BZ13,BZ14,BZ15,BZ16,BZ17,BZ18,BZ19,BZ20,BZ21,BZ22,BZ23,BZ24,BZ25,BZ26,BZ27,BZ28,BZ29,BZ30,BZ31,BZ32,BZ33,BZ34,BZ35,BZ36,BZ37,BZ38,BZ39,BZ40,BZ41,BZ42,BZ43,BZ44,BZ45,BZ46,BZ47,BZ48,BZ49,BZ50,BZ51,BZ52,BZ53,BZ54,BZ55,BZ56,BZ57,BZ58,BZ59,BZ60,XM,XB,XYID,ZYID,BJID,NJID,SJH,CSRQ,JG,MZMC,PYCCID,ZZMMMC,XQMC,ZJHM,ZJLX,XSLB,XZLX,RYZTID,ROLE_ID,USER_TYPE,PHOTO,deleted FROM SYT_USER_INFO WHERE XGH=? AND deleted=0
[2m2025-08-02 09:18:31.989[0;39m [32mDEBUG[0;39m [35m8258[0;39m [2m---[0;39m [2m[nio-9096-exec-5][0;39m [36mc.s.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m parse the finished SQL: SELECT XGH, BZ1, BZ2, BZ3, BZ4, BZ5, BZ6, BZ7, BZ8, BZ9, BZ10, BZ11, BZ12, BZ13, BZ14, BZ15, BZ16, BZ17, BZ18, BZ19, BZ20, BZ21, BZ22, BZ23, BZ24, BZ25, BZ26, BZ27, BZ28, BZ29, BZ30, BZ31, BZ32, BZ33, BZ34, BZ35, BZ36, BZ37, BZ38, BZ39, BZ40, BZ41, BZ42, BZ43, BZ44, BZ45, BZ46, BZ47, BZ48, BZ49, BZ50, BZ51, BZ52, BZ53, BZ54, BZ55, BZ56, BZ57, BZ58, BZ59, BZ60, XM, XB, XYID, ZYID, BJID, NJID, SJH, CSRQ, JG, MZMC, PYCCID, ZZMMMC, XQMC, ZJHM, ZJLX, XSLB, XZLX, RYZTID, ROLE_ID, USER_TYPE, PHOTO, deleted FROM SYT_USER_INFO WHERE XGH = ? AND deleted = 0
[2m2025-08-02 09:18:31.989[0;39m [32mDEBUG[0;39m [35m8258[0;39m [2m---[0;39m [2m[nio-9096-exec-5][0;39m [36mc.s.u.s.m.UserInfoMapper.selectById     [0;39m [2m:[0;39m ==>  Preparing: SELECT XGH, BZ1, BZ2, BZ3, BZ4, BZ5, BZ6, BZ7, BZ8, BZ9, BZ10, BZ11, BZ12, BZ13, BZ14, BZ15, BZ16, BZ17, BZ18, BZ19, BZ20, BZ21, BZ22, BZ23, BZ24, BZ25, BZ26, BZ27, BZ28, BZ29, BZ30, BZ31, BZ32, BZ33, BZ34, BZ35, BZ36, BZ37, BZ38, BZ39, BZ40, BZ41, BZ42, BZ43, BZ44, BZ45, BZ46, BZ47, BZ48, BZ49, BZ50, BZ51, BZ52, BZ53, BZ54, BZ55, BZ56, BZ57, BZ58, BZ59, BZ60, XM, XB, XYID, ZYID, BJID, NJID, SJH, CSRQ, JG, MZMC, PYCCID, ZZMMMC, XQMC, ZJHM, ZJLX, XSLB, XZLX, RYZTID, ROLE_ID, USER_TYPE, PHOTO, deleted FROM SYT_USER_INFO WHERE XGH = ? AND deleted = 0
[2m2025-08-02 09:18:31.990[0;39m [32mDEBUG[0;39m [35m8258[0;39m [2m---[0;39m [2m[nio-9096-exec-5][0;39m [36mc.s.u.s.m.UserInfoMapper.selectById     [0;39m [2m:[0;39m ==> Parameters: admin(String)
[2m2025-08-02 09:18:32.047[0;39m [32mDEBUG[0;39m [35m8258[0;39m [2m---[0;39m [2m[nio-9096-exec-3][0;39m [36mc.s.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m original SQL: SELECT    ID,NAME,SORT,CREATE_TIME    FROM  SYT_AIGC_APP_CLASS              ORDER BY sort ASC
[2m2025-08-02 09:18:32.052[0;39m [32mDEBUG[0;39m [35m8258[0;39m [2m---[0;39m [2m[nio-9096-exec-3][0;39m [36mc.s.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m SQL to parse, SQL: SELECT    ID,NAME,SORT,CREATE_TIME    FROM  SYT_AIGC_APP_CLASS              ORDER BY sort ASC
[2m2025-08-02 09:18:32.053[0;39m [32mDEBUG[0;39m [35m8258[0;39m [2m---[0;39m [2m[nio-9096-exec-3][0;39m [36mc.s.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m parse the finished SQL: SELECT ID, NAME, SORT, CREATE_TIME FROM SYT_AIGC_APP_CLASS ORDER BY sort ASC
[2m2025-08-02 09:18:32.055[0;39m [32mDEBUG[0;39m [35m8258[0;39m [2m---[0;39m [2m[nio-9096-exec-5][0;39m [36mc.s.u.s.m.UserInfoMapper.selectById     [0;39m [2m:[0;39m <==      Total: 1
[2m2025-08-02 09:18:32.055[0;39m [32mDEBUG[0;39m [35m8258[0;39m [2m---[0;39m [2m[nio-9096-exec-3][0;39m [36mc.s.l.a.m.AigcAppClassMapper.selectList [0;39m [2m:[0;39m ==>  Preparing: SELECT ID, NAME, SORT, CREATE_TIME FROM SYT_AIGC_APP_CLASS ORDER BY sort ASC
[2m2025-08-02 09:18:32.055[0;39m [32mDEBUG[0;39m [35m8258[0;39m [2m---[0;39m [2m[nio-9096-exec-3][0;39m [36mc.s.l.a.m.AigcAppClassMapper.selectList [0;39m [2m:[0;39m ==> Parameters: 
[2m2025-08-02 09:18:32.071[0;39m [32mDEBUG[0;39m [35m8258[0;39m [2m---[0;39m [2m[nio-9096-exec-5][0;39m [36mc.s.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m original SQL: SELECT XGH,BZ1,BZ2,BZ3,BZ4,BZ5,BZ6,BZ7,BZ8,BZ9,BZ10,BZ11,BZ12,BZ13,BZ14,BZ15,BZ16,BZ17,BZ18,BZ19,BZ20,BZ21,BZ22,BZ23,BZ24,BZ25,BZ26,BZ27,BZ28,BZ29,BZ30,BZ31,BZ32,BZ33,BZ34,BZ35,BZ36,BZ37,BZ38,BZ39,BZ40,BZ41,BZ42,BZ43,BZ44,BZ45,BZ46,BZ47,BZ48,BZ49,BZ50,BZ51,BZ52,BZ53,BZ54,BZ55,BZ56,BZ57,BZ58,BZ59,BZ60,XM,XB,XYID,ZYID,BJID,NJID,SJH,CSRQ,JG,MZMC,PYCCID,ZZMMMC,XQMC,ZJHM,ZJLX,XSLB,XZLX,RYZTID,ROLE_ID,USER_TYPE,PHOTO,deleted FROM SYT_USER_INFO WHERE XGH=? AND deleted=0
[2m2025-08-02 09:18:32.085[0;39m [32mDEBUG[0;39m [35m8258[0;39m [2m---[0;39m [2m[nio-9096-exec-5][0;39m [36mc.s.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m SQL to parse, SQL: SELECT XGH,BZ1,BZ2,BZ3,BZ4,BZ5,BZ6,BZ7,BZ8,BZ9,BZ10,BZ11,BZ12,BZ13,BZ14,BZ15,BZ16,BZ17,BZ18,BZ19,BZ20,BZ21,BZ22,BZ23,BZ24,BZ25,BZ26,BZ27,BZ28,BZ29,BZ30,BZ31,BZ32,BZ33,BZ34,BZ35,BZ36,BZ37,BZ38,BZ39,BZ40,BZ41,BZ42,BZ43,BZ44,BZ45,BZ46,BZ47,BZ48,BZ49,BZ50,BZ51,BZ52,BZ53,BZ54,BZ55,BZ56,BZ57,BZ58,BZ59,BZ60,XM,XB,XYID,ZYID,BJID,NJID,SJH,CSRQ,JG,MZMC,PYCCID,ZZMMMC,XQMC,ZJHM,ZJLX,XSLB,XZLX,RYZTID,ROLE_ID,USER_TYPE,PHOTO,deleted FROM SYT_USER_INFO WHERE XGH=? AND deleted=0
[2m2025-08-02 09:18:32.086[0;39m [32mDEBUG[0;39m [35m8258[0;39m [2m---[0;39m [2m[nio-9096-exec-5][0;39m [36mc.s.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m parse the finished SQL: SELECT XGH, BZ1, BZ2, BZ3, BZ4, BZ5, BZ6, BZ7, BZ8, BZ9, BZ10, BZ11, BZ12, BZ13, BZ14, BZ15, BZ16, BZ17, BZ18, BZ19, BZ20, BZ21, BZ22, BZ23, BZ24, BZ25, BZ26, BZ27, BZ28, BZ29, BZ30, BZ31, BZ32, BZ33, BZ34, BZ35, BZ36, BZ37, BZ38, BZ39, BZ40, BZ41, BZ42, BZ43, BZ44, BZ45, BZ46, BZ47, BZ48, BZ49, BZ50, BZ51, BZ52, BZ53, BZ54, BZ55, BZ56, BZ57, BZ58, BZ59, BZ60, XM, XB, XYID, ZYID, BJID, NJID, SJH, CSRQ, JG, MZMC, PYCCID, ZZMMMC, XQMC, ZJHM, ZJLX, XSLB, XZLX, RYZTID, ROLE_ID, USER_TYPE, PHOTO, deleted FROM SYT_USER_INFO WHERE XGH = ? AND deleted = 0
[2m2025-08-02 09:18:32.123[0;39m [32mDEBUG[0;39m [35m8258[0;39m [2m---[0;39m [2m[nio-9096-exec-3][0;39m [36mc.s.l.a.m.AigcAppClassMapper.selectList [0;39m [2m:[0;39m <==      Total: 3
[2m2025-08-02 09:18:32.123[0;39m [32mDEBUG[0;39m [35m8258[0;39m [2m---[0;39m [2m[nio-9096-exec-5][0;39m [36mc.s.u.s.m.UserInfoMapper.selectById     [0;39m [2m:[0;39m ==>  Preparing: SELECT XGH, BZ1, BZ2, BZ3, BZ4, BZ5, BZ6, BZ7, BZ8, BZ9, BZ10, BZ11, BZ12, BZ13, BZ14, BZ15, BZ16, BZ17, BZ18, BZ19, BZ20, BZ21, BZ22, BZ23, BZ24, BZ25, BZ26, BZ27, BZ28, BZ29, BZ30, BZ31, BZ32, BZ33, BZ34, BZ35, BZ36, BZ37, BZ38, BZ39, BZ40, BZ41, BZ42, BZ43, BZ44, BZ45, BZ46, BZ47, BZ48, BZ49, BZ50, BZ51, BZ52, BZ53, BZ54, BZ55, BZ56, BZ57, BZ58, BZ59, BZ60, XM, XB, XYID, ZYID, BJID, NJID, SJH, CSRQ, JG, MZMC, PYCCID, ZZMMMC, XQMC, ZJHM, ZJLX, XSLB, XZLX, RYZTID, ROLE_ID, USER_TYPE, PHOTO, deleted FROM SYT_USER_INFO WHERE XGH = ? AND deleted = 0
[2m2025-08-02 09:18:32.123[0;39m [32mDEBUG[0;39m [35m8258[0;39m [2m---[0;39m [2m[nio-9096-exec-5][0;39m [36mc.s.u.s.m.UserInfoMapper.selectById     [0;39m [2m:[0;39m ==> Parameters: admin(String)
[2m2025-08-02 09:18:32.186[0;39m [32mDEBUG[0;39m [35m8258[0;39m [2m---[0;39m [2m[nio-9096-exec-5][0;39m [36mc.s.u.s.m.UserInfoMapper.selectById     [0;39m [2m:[0;39m <==      Total: 1
[2m2025-08-02 09:18:32.213[0;39m [32mDEBUG[0;39m [35m8258[0;39m [2m---[0;39m [2m[nio-9096-exec-5][0;39m [36mc.s.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m original SQL: SELECT  DISTINCT    t.ID,t.CLASS_ID,t.DES,t.PROMPT,t.NAME,t.CREATE_TIME,t.SAVE_TIME,t.COVER,t.KNOWLEDGE_IDS,t.TOOL_IDS,t.MCP_SERVER_ID,t.MODEL_ID,t.PROLOGUE,t.STATUS,t.MAX_RESULTS,t.MIN_SCORE,t.PRESET_QUESTION   FROM SYT_AIGC_APP  t    LEFT JOIN SYT_SELECTOR_ACCOUNT acc ON (acc.ITEM_ID = t.ID)     WHERE   ((acc.XGH = ? OR acc.ITEM_ID IS NULL) AND t.STATUS = ?) ORDER BY t.CREATE_TIME DESC
[2m2025-08-02 09:18:32.222[0;39m [32mDEBUG[0;39m [35m8258[0;39m [2m---[0;39m [2m[nio-9096-exec-5][0;39m [36mc.s.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m SQL to parse, SQL: SELECT  DISTINCT    t.ID,t.CLASS_ID,t.DES,t.PROMPT,t.NAME,t.CREATE_TIME,t.SAVE_TIME,t.COVER,t.KNOWLEDGE_IDS,t.TOOL_IDS,t.MCP_SERVER_ID,t.MODEL_ID,t.PROLOGUE,t.STATUS,t.MAX_RESULTS,t.MIN_SCORE,t.PRESET_QUESTION   FROM SYT_AIGC_APP  t    LEFT JOIN SYT_SELECTOR_ACCOUNT acc ON (acc.ITEM_ID = t.ID)     WHERE   ((acc.XGH = ? OR acc.ITEM_ID IS NULL) AND t.STATUS = ?) ORDER BY t.CREATE_TIME DESC
[2m2025-08-02 09:18:32.224[0;39m [32mDEBUG[0;39m [35m8258[0;39m [2m---[0;39m [2m[nio-9096-exec-5][0;39m [36mc.s.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m parse the finished SQL: SELECT DISTINCT t.ID, t.CLASS_ID, t.DES, t.PROMPT, t.NAME, t.CREATE_TIME, t.SAVE_TIME, t.COVER, t.KNOWLEDGE_IDS, t.TOOL_IDS, t.MCP_SERVER_ID, t.MODEL_ID, t.PROLOGUE, t.STATUS, t.MAX_RESULTS, t.MIN_SCORE, t.PRESET_QUESTION FROM SYT_AIGC_APP t LEFT JOIN SYT_SELECTOR_ACCOUNT acc ON (acc.ITEM_ID = t.ID) WHERE ((acc.XGH = ? OR acc.ITEM_ID IS NULL) AND t.STATUS = ?) ORDER BY t.CREATE_TIME DESC
[2m2025-08-02 09:18:32.238[0;39m [32mDEBUG[0;39m [35m8258[0;39m [2m---[0;39m [2m[nio-9096-exec-5][0;39m [36myth-langchain-api-entity-AigcApp_mpCount[0;39m [2m:[0;39m ==>  Preparing: SELECT COUNT(*) FROM (SELECT DISTINCT t.ID, t.CLASS_ID, t.DES, t.PROMPT, t.NAME, t.CREATE_TIME, t.SAVE_TIME, t.COVER, t.KNOWLEDGE_IDS, t.TOOL_IDS, t.MCP_SERVER_ID, t.MODEL_ID, t.PROLOGUE, t.STATUS, t.MAX_RESULTS, t.MIN_SCORE, t.PRESET_QUESTION FROM SYT_AIGC_APP t LEFT JOIN SYT_SELECTOR_ACCOUNT acc ON (acc.ITEM_ID = t.ID) WHERE ((acc.XGH = ? OR acc.ITEM_ID IS NULL) AND t.STATUS = ?) ORDER BY t.CREATE_TIME DESC) TOTAL
[2m2025-08-02 09:18:32.242[0;39m [32mDEBUG[0;39m [35m8258[0;39m [2m---[0;39m [2m[nio-9096-exec-5][0;39m [36myth-langchain-api-entity-AigcApp_mpCount[0;39m [2m:[0;39m ==> Parameters: admin(String), 1(Integer)
[2m2025-08-02 09:18:32.322[0;39m [32mDEBUG[0;39m [35m8258[0;39m [2m---[0;39m [2m[nio-9096-exec-5][0;39m [36myth-langchain-api-entity-AigcApp_mpCount[0;39m [2m:[0;39m <==      Total: 1
[2m2025-08-02 09:18:32.330[0;39m [32mDEBUG[0;39m [35m8258[0;39m [2m---[0;39m [2m[nio-9096-exec-5][0;39m [36m-com-sanyth-langchain-api-entity-AigcApp[0;39m [2m:[0;39m ==>  Preparing: SELECT * FROM ( SELECT TMP.*, ROWNUM ROW_ID FROM ( SELECT DISTINCT t.ID, t.CLASS_ID, t.DES, t.PROMPT, t.NAME, t.CREATE_TIME, t.SAVE_TIME, t.COVER, t.KNOWLEDGE_IDS, t.TOOL_IDS, t.MCP_SERVER_ID, t.MODEL_ID, t.PROLOGUE, t.STATUS, t.MAX_RESULTS, t.MIN_SCORE, t.PRESET_QUESTION FROM SYT_AIGC_APP t LEFT JOIN SYT_SELECTOR_ACCOUNT acc ON (acc.ITEM_ID = t.ID) WHERE ((acc.XGH = ? OR acc.ITEM_ID IS NULL) AND t.STATUS = ?) ORDER BY t.CREATE_TIME DESC ) TMP WHERE ROWNUM <=?) WHERE ROW_ID > ?
[2m2025-08-02 09:18:32.332[0;39m [32mDEBUG[0;39m [35m8258[0;39m [2m---[0;39m [2m[nio-9096-exec-5][0;39m [36m-com-sanyth-langchain-api-entity-AigcApp[0;39m [2m:[0;39m ==> Parameters: admin(String), 1(Integer), 20(Long), 0(Long)
[2m2025-08-02 09:18:32.414[0;39m [32mDEBUG[0;39m [35m8258[0;39m [2m---[0;39m [2m[nio-9096-exec-5][0;39m [36m-com-sanyth-langchain-api-entity-AigcApp[0;39m [2m:[0;39m <==      Total: 3
[2m2025-08-02 09:19:11.955[0;39m [32mDEBUG[0;39m [35m8258[0;39m [2m---[0;39m [2m[io-9096-exec-10][0;39m [36mc.s.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m original SQL: SELECT    ID,PARAM_NAME,PARAM_VALUE,PARAM_MODE,DESCRIPTION,ATTACHMENT,create_time    FROM  SYT_SYS_PARAM
[2m2025-08-02 09:19:11.967[0;39m [32mDEBUG[0;39m [35m8258[0;39m [2m---[0;39m [2m[io-9096-exec-10][0;39m [36mc.s.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m SQL to parse, SQL: SELECT    ID,PARAM_NAME,PARAM_VALUE,PARAM_MODE,DESCRIPTION,ATTACHMENT,create_time    FROM  SYT_SYS_PARAM
[2m2025-08-02 09:19:11.971[0;39m [32mDEBUG[0;39m [35m8258[0;39m [2m---[0;39m [2m[io-9096-exec-10][0;39m [36mc.s.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m parse the finished SQL: SELECT ID, PARAM_NAME, PARAM_VALUE, PARAM_MODE, DESCRIPTION, ATTACHMENT, create_time FROM SYT_SYS_PARAM
[2m2025-08-02 09:19:11.984[0;39m [32mDEBUG[0;39m [35m8258[0;39m [2m---[0;39m [2m[io-9096-exec-10][0;39m [36mc.s.u.s.m.S.selectList_mpCount          [0;39m [2m:[0;39m ==>  Preparing: SELECT COUNT(*) AS total FROM SYT_SYS_PARAM
[2m2025-08-02 09:19:11.985[0;39m [32mDEBUG[0;39m [35m8258[0;39m [2m---[0;39m [2m[io-9096-exec-10][0;39m [36mc.s.u.s.m.S.selectList_mpCount          [0;39m [2m:[0;39m ==> Parameters: 
[2m2025-08-02 09:19:12.079[0;39m [32mDEBUG[0;39m [35m8258[0;39m [2m---[0;39m [2m[io-9096-exec-10][0;39m [36mc.s.u.s.m.S.selectList_mpCount          [0;39m [2m:[0;39m <==      Total: 1
[2m2025-08-02 09:19:12.097[0;39m [32mDEBUG[0;39m [35m8258[0;39m [2m---[0;39m [2m[io-9096-exec-10][0;39m [36mc.s.u.s.m.SysParamMapper.selectList     [0;39m [2m:[0;39m ==>  Preparing: SELECT * FROM ( SELECT TMP.*, ROWNUM ROW_ID FROM ( SELECT ID, PARAM_NAME, PARAM_VALUE, PARAM_MODE, DESCRIPTION, ATTACHMENT, create_time FROM SYT_SYS_PARAM ORDER BY create_time DESC ) TMP WHERE ROWNUM <=?) WHERE ROW_ID > ?
[2m2025-08-02 09:19:12.098[0;39m [32mDEBUG[0;39m [35m8258[0;39m [2m---[0;39m [2m[io-9096-exec-10][0;39m [36mc.s.u.s.m.SysParamMapper.selectList     [0;39m [2m:[0;39m ==> Parameters: 10(Long), 0(Long)
[2m2025-08-02 09:19:12.178[0;39m [32mDEBUG[0;39m [35m8258[0;39m [2m---[0;39m [2m[io-9096-exec-10][0;39m [36mc.s.u.s.m.SysParamMapper.selectList     [0;39m [2m:[0;39m <==      Total: 1
[2m2025-08-02 09:38:11.108[0;39m [32mDEBUG[0;39m [35m8258[0;39m [2m---[0;39m [2m[nio-9096-exec-8][0;39m [36mc.s.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m original SQL: SELECT XGH,BZ1,BZ2,BZ3,BZ4,BZ5,BZ6,BZ7,BZ8,BZ9,BZ10,BZ11,BZ12,BZ13,BZ14,BZ15,BZ16,BZ17,BZ18,BZ19,BZ20,BZ21,BZ22,BZ23,BZ24,BZ25,BZ26,BZ27,BZ28,BZ29,BZ30,BZ31,BZ32,BZ33,BZ34,BZ35,BZ36,BZ37,BZ38,BZ39,BZ40,BZ41,BZ42,BZ43,BZ44,BZ45,BZ46,BZ47,BZ48,BZ49,BZ50,BZ51,BZ52,BZ53,BZ54,BZ55,BZ56,BZ57,BZ58,BZ59,BZ60,XM,XB,XYID,ZYID,BJID,NJID,SJH,CSRQ,JG,MZMC,PYCCID,ZZMMMC,XQMC,ZJHM,ZJLX,XSLB,XZLX,RYZTID,ROLE_ID,USER_TYPE,PHOTO,deleted FROM SYT_USER_INFO WHERE XGH=? AND deleted=0
[2m2025-08-02 09:38:11.243[0;39m [32mDEBUG[0;39m [35m8258[0;39m [2m---[0;39m [2m[nio-9096-exec-8][0;39m [36mc.s.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m SQL to parse, SQL: SELECT XGH,BZ1,BZ2,BZ3,BZ4,BZ5,BZ6,BZ7,BZ8,BZ9,BZ10,BZ11,BZ12,BZ13,BZ14,BZ15,BZ16,BZ17,BZ18,BZ19,BZ20,BZ21,BZ22,BZ23,BZ24,BZ25,BZ26,BZ27,BZ28,BZ29,BZ30,BZ31,BZ32,BZ33,BZ34,BZ35,BZ36,BZ37,BZ38,BZ39,BZ40,BZ41,BZ42,BZ43,BZ44,BZ45,BZ46,BZ47,BZ48,BZ49,BZ50,BZ51,BZ52,BZ53,BZ54,BZ55,BZ56,BZ57,BZ58,BZ59,BZ60,XM,XB,XYID,ZYID,BJID,NJID,SJH,CSRQ,JG,MZMC,PYCCID,ZZMMMC,XQMC,ZJHM,ZJLX,XSLB,XZLX,RYZTID,ROLE_ID,USER_TYPE,PHOTO,deleted FROM SYT_USER_INFO WHERE XGH=? AND deleted=0
[2m2025-08-02 09:38:11.244[0;39m [32mDEBUG[0;39m [35m8258[0;39m [2m---[0;39m [2m[nio-9096-exec-8][0;39m [36mc.s.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m parse the finished SQL: SELECT XGH, BZ1, BZ2, BZ3, BZ4, BZ5, BZ6, BZ7, BZ8, BZ9, BZ10, BZ11, BZ12, BZ13, BZ14, BZ15, BZ16, BZ17, BZ18, BZ19, BZ20, BZ21, BZ22, BZ23, BZ24, BZ25, BZ26, BZ27, BZ28, BZ29, BZ30, BZ31, BZ32, BZ33, BZ34, BZ35, BZ36, BZ37, BZ38, BZ39, BZ40, BZ41, BZ42, BZ43, BZ44, BZ45, BZ46, BZ47, BZ48, BZ49, BZ50, BZ51, BZ52, BZ53, BZ54, BZ55, BZ56, BZ57, BZ58, BZ59, BZ60, XM, XB, XYID, ZYID, BJID, NJID, SJH, CSRQ, JG, MZMC, PYCCID, ZZMMMC, XQMC, ZJHM, ZJLX, XSLB, XZLX, RYZTID, ROLE_ID, USER_TYPE, PHOTO, deleted FROM SYT_USER_INFO WHERE XGH = ? AND deleted = 0
[2m2025-08-02 09:38:11.308[0;39m [32mDEBUG[0;39m [35m8258[0;39m [2m---[0;39m [2m[nio-9096-exec-8][0;39m [36mc.s.u.s.m.UserInfoMapper.selectById     [0;39m [2m:[0;39m ==>  Preparing: SELECT XGH, BZ1, BZ2, BZ3, BZ4, BZ5, BZ6, BZ7, BZ8, BZ9, BZ10, BZ11, BZ12, BZ13, BZ14, BZ15, BZ16, BZ17, BZ18, BZ19, BZ20, BZ21, BZ22, BZ23, BZ24, BZ25, BZ26, BZ27, BZ28, BZ29, BZ30, BZ31, BZ32, BZ33, BZ34, BZ35, BZ36, BZ37, BZ38, BZ39, BZ40, BZ41, BZ42, BZ43, BZ44, BZ45, BZ46, BZ47, BZ48, BZ49, BZ50, BZ51, BZ52, BZ53, BZ54, BZ55, BZ56, BZ57, BZ58, BZ59, BZ60, XM, XB, XYID, ZYID, BJID, NJID, SJH, CSRQ, JG, MZMC, PYCCID, ZZMMMC, XQMC, ZJHM, ZJLX, XSLB, XZLX, RYZTID, ROLE_ID, USER_TYPE, PHOTO, deleted FROM SYT_USER_INFO WHERE XGH = ? AND deleted = 0
[2m2025-08-02 09:38:11.315[0;39m [32mDEBUG[0;39m [35m8258[0;39m [2m---[0;39m [2m[nio-9096-exec-8][0;39m [36mc.s.u.s.m.UserInfoMapper.selectById     [0;39m [2m:[0;39m ==> Parameters: admin(String)
[2m2025-08-02 09:38:11.388[0;39m [32mDEBUG[0;39m [35m8258[0;39m [2m---[0;39m [2m[nio-9096-exec-8][0;39m [36mc.s.u.s.m.UserInfoMapper.selectById     [0;39m [2m:[0;39m <==      Total: 1
[2m2025-08-02 09:38:11.874[0;39m [32mDEBUG[0;39m [35m8258[0;39m [2m---[0;39m [2m[nio-9096-exec-7][0;39m [36mc.s.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m original SQL: SELECT    ID,PARAM_NAME,PARAM_VALUE,PARAM_MODE,DESCRIPTION,ATTACHMENT,create_time    FROM  SYT_SYS_PARAM
[2m2025-08-02 09:38:11.881[0;39m [32mDEBUG[0;39m [35m8258[0;39m [2m---[0;39m [2m[nio-9096-exec-7][0;39m [36mc.s.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m SQL to parse, SQL: SELECT    ID,PARAM_NAME,PARAM_VALUE,PARAM_MODE,DESCRIPTION,ATTACHMENT,create_time    FROM  SYT_SYS_PARAM
[2m2025-08-02 09:38:11.884[0;39m [32mDEBUG[0;39m [35m8258[0;39m [2m---[0;39m [2m[nio-9096-exec-7][0;39m [36mc.s.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m parse the finished SQL: SELECT ID, PARAM_NAME, PARAM_VALUE, PARAM_MODE, DESCRIPTION, ATTACHMENT, create_time FROM SYT_SYS_PARAM
[2m2025-08-02 09:38:11.893[0;39m [32mDEBUG[0;39m [35m8258[0;39m [2m---[0;39m [2m[nio-9096-exec-7][0;39m [36mc.s.u.s.m.S.selectList_mpCount          [0;39m [2m:[0;39m ==>  Preparing: SELECT COUNT(*) AS total FROM SYT_SYS_PARAM
[2m2025-08-02 09:38:11.893[0;39m [32mDEBUG[0;39m [35m8258[0;39m [2m---[0;39m [2m[nio-9096-exec-7][0;39m [36mc.s.u.s.m.S.selectList_mpCount          [0;39m [2m:[0;39m ==> Parameters: 
[2m2025-08-02 09:38:11.955[0;39m [32mDEBUG[0;39m [35m8258[0;39m [2m---[0;39m [2m[nio-9096-exec-7][0;39m [36mc.s.u.s.m.S.selectList_mpCount          [0;39m [2m:[0;39m <==      Total: 1
[2m2025-08-02 09:38:11.965[0;39m [32mDEBUG[0;39m [35m8258[0;39m [2m---[0;39m [2m[nio-9096-exec-7][0;39m [36mc.s.u.s.m.SysParamMapper.selectList     [0;39m [2m:[0;39m ==>  Preparing: SELECT * FROM ( SELECT TMP.*, ROWNUM ROW_ID FROM ( SELECT ID, PARAM_NAME, PARAM_VALUE, PARAM_MODE, DESCRIPTION, ATTACHMENT, create_time FROM SYT_SYS_PARAM ORDER BY create_time DESC ) TMP WHERE ROWNUM <=?) WHERE ROW_ID > ?
[2m2025-08-02 09:38:11.966[0;39m [32mDEBUG[0;39m [35m8258[0;39m [2m---[0;39m [2m[nio-9096-exec-7][0;39m [36mc.s.u.s.m.SysParamMapper.selectList     [0;39m [2m:[0;39m ==> Parameters: 10(Long), 0(Long)
[2m2025-08-02 09:38:12.027[0;39m [32mDEBUG[0;39m [35m8258[0;39m [2m---[0;39m [2m[nio-9096-exec-7][0;39m [36mc.s.u.s.m.SysParamMapper.selectList     [0;39m [2m:[0;39m <==      Total: 1
[2m2025-08-02 09:46:15.938[0;39m [32m INFO[0;39m [35m8258[0;39m [2m---[0;39m [2m[168.9.254:37017][0;39m [36morg.mongodb.driver.cluster              [0;39m [2m:[0;39m Exception in monitor thread while connecting to server *************:37017

com.mongodb.MongoSocketReadTimeoutException: Timeout while receiving message
	at com.mongodb.internal.connection.InternalStreamConnection.createReadTimeoutException(InternalStreamConnection.java:819)
	at com.mongodb.internal.connection.InternalStreamConnection.translateReadException(InternalStreamConnection.java:807)
	at com.mongodb.internal.connection.InternalStreamConnection.receiveResponseBuffers(InternalStreamConnection.java:857)
	at com.mongodb.internal.connection.InternalStreamConnection.receiveCommandMessageResponse(InternalStreamConnection.java:517)
	at com.mongodb.internal.connection.InternalStreamConnection.receive(InternalStreamConnection.java:469)
	at com.mongodb.internal.connection.DefaultServerMonitor$ServerMonitor.lookupServerDescription(DefaultServerMonitor.java:249)
	at com.mongodb.internal.connection.DefaultServerMonitor$ServerMonitor.run(DefaultServerMonitor.java:176)
Caused by: java.net.SocketTimeoutException: Read timed out
	at java.base/sun.nio.ch.NioSocketImpl.timedRead(NioSocketImpl.java:288)
	at java.base/sun.nio.ch.NioSocketImpl.implRead(NioSocketImpl.java:314)
	at java.base/sun.nio.ch.NioSocketImpl.read(NioSocketImpl.java:355)
	at java.base/sun.nio.ch.NioSocketImpl$1.read(NioSocketImpl.java:808)
	at java.base/java.net.Socket$SocketInputStream.read(Socket.java:976)
	at com.mongodb.internal.connection.SocketStream.read(SocketStream.java:182)
	at com.mongodb.internal.connection.InternalStreamConnection.receiveResponseBuffers(InternalStreamConnection.java:824)
	... 4 common frames omitted

[2m2025-08-02 09:46:16.133[0;39m [32m INFO[0;39m [35m8258[0;39m [2m---[0;39m [2m[168.9.254:37017][0;39m [36morg.mongodb.driver.cluster              [0;39m [2m:[0;39m Monitor thread successfully connected to server with description ServerDescription{address=*************:37017, type=STANDALONE, cryptd=false, state=CONNECTED, ok=true, minWireVersion=0, maxWireVersion=13, maxDocumentSize=16777216, logicalSessionTimeoutMinutes=30, roundTripTimeNanos=72819375, minRoundTripTimeNanos=0}
[2m2025-08-02 10:02:08.289[0;39m [32m INFO[0;39m [35m8258[0;39m [2m---[0;39m [2m[168.9.254:37017][0;39m [36morg.mongodb.driver.cluster              [0;39m [2m:[0;39m Exception in monitor thread while connecting to server *************:37017

com.mongodb.MongoSocketReadTimeoutException: Timeout while receiving message
	at com.mongodb.internal.connection.InternalStreamConnection.createReadTimeoutException(InternalStreamConnection.java:819)
	at com.mongodb.internal.connection.InternalStreamConnection.translateReadException(InternalStreamConnection.java:807)
	at com.mongodb.internal.connection.InternalStreamConnection.receiveResponseBuffers(InternalStreamConnection.java:857)
	at com.mongodb.internal.connection.InternalStreamConnection.receiveCommandMessageResponse(InternalStreamConnection.java:517)
	at com.mongodb.internal.connection.InternalStreamConnection.receive(InternalStreamConnection.java:469)
	at com.mongodb.internal.connection.DefaultServerMonitor$ServerMonitor.lookupServerDescription(DefaultServerMonitor.java:249)
	at com.mongodb.internal.connection.DefaultServerMonitor$ServerMonitor.run(DefaultServerMonitor.java:176)
Caused by: java.net.SocketTimeoutException: Read timed out
	at java.base/sun.nio.ch.NioSocketImpl.timedRead(NioSocketImpl.java:288)
	at java.base/sun.nio.ch.NioSocketImpl.implRead(NioSocketImpl.java:314)
	at java.base/sun.nio.ch.NioSocketImpl.read(NioSocketImpl.java:355)
	at java.base/sun.nio.ch.NioSocketImpl$1.read(NioSocketImpl.java:808)
	at java.base/java.net.Socket$SocketInputStream.read(Socket.java:976)
	at com.mongodb.internal.connection.SocketStream.read(SocketStream.java:182)
	at com.mongodb.internal.connection.InternalStreamConnection.receiveResponseBuffers(InternalStreamConnection.java:824)
	... 4 common frames omitted

[2m2025-08-02 10:02:08.424[0;39m [32m INFO[0;39m [35m8258[0;39m [2m---[0;39m [2m[168.9.254:37017][0;39m [36morg.mongodb.driver.cluster              [0;39m [2m:[0;39m Monitor thread successfully connected to server with description ServerDescription{address=*************:37017, type=STANDALONE, cryptd=false, state=CONNECTED, ok=true, minWireVersion=0, maxWireVersion=13, maxDocumentSize=16777216, logicalSessionTimeoutMinutes=30, roundTripTimeNanos=58917292, minRoundTripTimeNanos=0}
[2m2025-08-02 10:02:18.929[0;39m [31mERROR[0;39m [35m8258[0;39m [2m---[0;39m [2m[sson-netty-1-10][0;39m [36mo.r.client.handler.ErrorsLoggingHandler [0;39m [2m:[0;39m Exception occured. Channel: [id: 0x4cf35047, L:/************:56752 - R:*************/*************:6380]

java.io.IOException: Operation timed out
	at java.base/sun.nio.ch.SocketDispatcher.read0(Native Method)
	at java.base/sun.nio.ch.SocketDispatcher.read(SocketDispatcher.java:47)
	at java.base/sun.nio.ch.IOUtil.readIntoNativeBuffer(IOUtil.java:330)
	at java.base/sun.nio.ch.IOUtil.read(IOUtil.java:284)
	at java.base/sun.nio.ch.IOUtil.read(IOUtil.java:259)
	at java.base/sun.nio.ch.SocketChannelImpl.read(SocketChannelImpl.java:417)
	at io.netty.buffer.PooledByteBuf.setBytes(PooledByteBuf.java:255)
	at io.netty.buffer.AbstractByteBuf.writeBytes(AbstractByteBuf.java:1132)
	at io.netty.channel.socket.nio.NioSocketChannel.doReadBytes(NioSocketChannel.java:356)
	at io.netty.channel.nio.AbstractNioByteChannel$NioByteUnsafe.read(AbstractNioByteChannel.java:151)
	at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:796)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:732)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:658)
	at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:562)
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:998)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.base/java.lang.Thread.run(Thread.java:840)

[2m2025-08-02 10:02:23.648[0;39m [31mERROR[0;39m [35m8258[0;39m [2m---[0;39m [2m[isson-timer-4-1][0;39m [36mo.r.c.handler.PingConnectionHandler     [0;39m [2m:[0;39m Unable to send PING command over channel: [id: 0xd8a6cfba, L:/************:56743 - R:*************/*************:6380]

org.redisson.client.RedisTimeoutException: Command execution timeout for command: (PING), params: [], Redis client: [addr=redis://*************:6380]
	at org.redisson.client.RedisConnection.lambda$async$2(RedisConnection.java:295)
	at io.netty.util.HashedWheelTimer$HashedWheelTimeout.run(HashedWheelTimer.java:713)
	at io.netty.util.concurrent.ImmediateExecutor.execute(ImmediateExecutor.java:34)
	at io.netty.util.HashedWheelTimer$HashedWheelTimeout.expire(HashedWheelTimer.java:701)
	at io.netty.util.HashedWheelTimer$HashedWheelBucket.expireTimeouts(HashedWheelTimer.java:787)
	at io.netty.util.HashedWheelTimer$Worker.run(HashedWheelTimer.java:501)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.base/java.lang.Thread.run(Thread.java:840)

[2m2025-08-02 10:02:23.757[0;39m [31mERROR[0;39m [35m8258[0;39m [2m---[0;39m [2m[isson-timer-4-1][0;39m [36mo.r.c.handler.PingConnectionHandler     [0;39m [2m:[0;39m Unable to send PING command over channel: [id: 0x19a16271, L:/************:56742 - R:*************/*************:6380]

org.redisson.client.RedisTimeoutException: Command execution timeout for command: (PING), params: [], Redis client: [addr=redis://*************:6380]
	at org.redisson.client.RedisConnection.lambda$async$2(RedisConnection.java:295)
	at io.netty.util.HashedWheelTimer$HashedWheelTimeout.run(HashedWheelTimer.java:713)
	at io.netty.util.concurrent.ImmediateExecutor.execute(ImmediateExecutor.java:34)
	at io.netty.util.HashedWheelTimer$HashedWheelTimeout.expire(HashedWheelTimer.java:701)
	at io.netty.util.HashedWheelTimer$HashedWheelBucket.expireTimeouts(HashedWheelTimer.java:787)
	at io.netty.util.HashedWheelTimer$Worker.run(HashedWheelTimer.java:501)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.base/java.lang.Thread.run(Thread.java:840)

[2m2025-08-02 10:02:23.855[0;39m [31mERROR[0;39m [35m8258[0;39m [2m---[0;39m [2m[isson-timer-4-1][0;39m [36mo.r.c.handler.PingConnectionHandler     [0;39m [2m:[0;39m Unable to send PING command over channel: [id: 0xf05729ea, L:/************:56749 - R:*************/*************:6380]

org.redisson.client.RedisTimeoutException: Command execution timeout for command: (PING), params: [], Redis client: [addr=redis://*************:6380]
	at org.redisson.client.RedisConnection.lambda$async$2(RedisConnection.java:295)
	at io.netty.util.HashedWheelTimer$HashedWheelTimeout.run(HashedWheelTimer.java:713)
	at io.netty.util.concurrent.ImmediateExecutor.execute(ImmediateExecutor.java:34)
	at io.netty.util.HashedWheelTimer$HashedWheelTimeout.expire(HashedWheelTimer.java:701)
	at io.netty.util.HashedWheelTimer$HashedWheelBucket.expireTimeouts(HashedWheelTimer.java:787)
	at io.netty.util.HashedWheelTimer$Worker.run(HashedWheelTimer.java:501)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.base/java.lang.Thread.run(Thread.java:840)

[2m2025-08-02 10:02:23.955[0;39m [31mERROR[0;39m [35m8258[0;39m [2m---[0;39m [2m[isson-timer-4-1][0;39m [36mo.r.c.handler.PingConnectionHandler     [0;39m [2m:[0;39m Unable to send PING command over channel: [id: 0x6b063b22, L:/************:56751 - R:*************/*************:6380]

org.redisson.client.RedisTimeoutException: Command execution timeout for command: (PING), params: [], Redis client: [addr=redis://*************:6380]
	at org.redisson.client.RedisConnection.lambda$async$2(RedisConnection.java:295)
	at io.netty.util.HashedWheelTimer$HashedWheelTimeout.run(HashedWheelTimer.java:713)
	at io.netty.util.concurrent.ImmediateExecutor.execute(ImmediateExecutor.java:34)
	at io.netty.util.HashedWheelTimer$HashedWheelTimeout.expire(HashedWheelTimer.java:701)
	at io.netty.util.HashedWheelTimer$HashedWheelBucket.expireTimeouts(HashedWheelTimer.java:787)
	at io.netty.util.HashedWheelTimer$Worker.run(HashedWheelTimer.java:501)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.base/java.lang.Thread.run(Thread.java:840)

[2m2025-08-02 10:02:24.253[0;39m [31mERROR[0;39m [35m8258[0;39m [2m---[0;39m [2m[isson-timer-4-1][0;39m [36mo.r.c.handler.PingConnectionHandler     [0;39m [2m:[0;39m Unable to send PING command over channel: [id: 0xa3475977, L:/************:56754 - R:*************/*************:6380]

org.redisson.client.RedisTimeoutException: Command execution timeout for command: (PING), params: [], Redis client: [addr=redis://*************:6380]
	at org.redisson.client.RedisConnection.lambda$async$2(RedisConnection.java:295)
	at io.netty.util.HashedWheelTimer$HashedWheelTimeout.run(HashedWheelTimer.java:713)
	at io.netty.util.concurrent.ImmediateExecutor.execute(ImmediateExecutor.java:34)
	at io.netty.util.HashedWheelTimer$HashedWheelTimeout.expire(HashedWheelTimer.java:701)
	at io.netty.util.HashedWheelTimer$HashedWheelBucket.expireTimeouts(HashedWheelTimer.java:787)
	at io.netty.util.HashedWheelTimer$Worker.run(HashedWheelTimer.java:501)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.base/java.lang.Thread.run(Thread.java:840)

[2m2025-08-02 10:02:24.352[0;39m [31mERROR[0;39m [35m8258[0;39m [2m---[0;39m [2m[isson-timer-4-1][0;39m [36mo.r.c.handler.PingConnectionHandler     [0;39m [2m:[0;39m Unable to send PING command over channel: [id: 0x64e7c079, L:/************:56757 - R:*************/*************:6380]

org.redisson.client.RedisTimeoutException: Command execution timeout for command: (PING), params: [], Redis client: [addr=redis://*************:6380]
	at org.redisson.client.RedisConnection.lambda$async$2(RedisConnection.java:295)
	at io.netty.util.HashedWheelTimer$HashedWheelTimeout.run(HashedWheelTimer.java:713)
	at io.netty.util.concurrent.ImmediateExecutor.execute(ImmediateExecutor.java:34)
	at io.netty.util.HashedWheelTimer$HashedWheelTimeout.expire(HashedWheelTimer.java:701)
	at io.netty.util.HashedWheelTimer$HashedWheelBucket.expireTimeouts(HashedWheelTimer.java:787)
	at io.netty.util.HashedWheelTimer$Worker.run(HashedWheelTimer.java:501)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.base/java.lang.Thread.run(Thread.java:840)

[2m2025-08-02 10:02:24.556[0;39m [31mERROR[0;39m [35m8258[0;39m [2m---[0;39m [2m[isson-timer-4-1][0;39m [36mo.r.c.handler.PingConnectionHandler     [0;39m [2m:[0;39m Unable to send PING command over channel: [id: 0xff30127a, L:/************:56759 - R:*************/*************:6380]

org.redisson.client.RedisTimeoutException: Command execution timeout for command: (PING), params: [], Redis client: [addr=redis://*************:6380]
	at org.redisson.client.RedisConnection.lambda$async$2(RedisConnection.java:295)
	at io.netty.util.HashedWheelTimer$HashedWheelTimeout.run(HashedWheelTimer.java:713)
	at io.netty.util.concurrent.ImmediateExecutor.execute(ImmediateExecutor.java:34)
	at io.netty.util.HashedWheelTimer$HashedWheelTimeout.expire(HashedWheelTimer.java:701)
	at io.netty.util.HashedWheelTimer$HashedWheelBucket.expireTimeouts(HashedWheelTimer.java:787)
	at io.netty.util.HashedWheelTimer$Worker.run(HashedWheelTimer.java:501)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.base/java.lang.Thread.run(Thread.java:840)

[2m2025-08-02 10:02:24.650[0;39m [31mERROR[0;39m [35m8258[0;39m [2m---[0;39m [2m[isson-timer-4-1][0;39m [36mo.r.c.handler.PingConnectionHandler     [0;39m [2m:[0;39m Unable to send PING command over channel: [id: 0x5700f22f, L:/************:56760 - R:*************/*************:6380]

org.redisson.client.RedisTimeoutException: Command execution timeout for command: (PING), params: [], Redis client: [addr=redis://*************:6380]
	at org.redisson.client.RedisConnection.lambda$async$2(RedisConnection.java:295)
	at io.netty.util.HashedWheelTimer$HashedWheelTimeout.run(HashedWheelTimer.java:713)
	at io.netty.util.concurrent.ImmediateExecutor.execute(ImmediateExecutor.java:34)
	at io.netty.util.HashedWheelTimer$HashedWheelTimeout.expire(HashedWheelTimer.java:701)
	at io.netty.util.HashedWheelTimer$HashedWheelBucket.expireTimeouts(HashedWheelTimer.java:787)
	at io.netty.util.HashedWheelTimer$Worker.run(HashedWheelTimer.java:501)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.base/java.lang.Thread.run(Thread.java:840)

[2m2025-08-02 10:02:24.756[0;39m [31mERROR[0;39m [35m8258[0;39m [2m---[0;39m [2m[isson-timer-4-1][0;39m [36mo.r.c.handler.PingConnectionHandler     [0;39m [2m:[0;39m Unable to send PING command over channel: [id: 0x6dbb856d, L:/************:56762 - R:*************/*************:6380]

org.redisson.client.RedisTimeoutException: Command execution timeout for command: (PING), params: [], Redis client: [addr=redis://*************:6380]
	at org.redisson.client.RedisConnection.lambda$async$2(RedisConnection.java:295)
	at io.netty.util.HashedWheelTimer$HashedWheelTimeout.run(HashedWheelTimer.java:713)
	at io.netty.util.concurrent.ImmediateExecutor.execute(ImmediateExecutor.java:34)
	at io.netty.util.HashedWheelTimer$HashedWheelTimeout.expire(HashedWheelTimer.java:701)
	at io.netty.util.HashedWheelTimer$HashedWheelBucket.expireTimeouts(HashedWheelTimer.java:787)
	at io.netty.util.HashedWheelTimer$Worker.run(HashedWheelTimer.java:501)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.base/java.lang.Thread.run(Thread.java:840)

[2m2025-08-02 10:02:24.956[0;39m [31mERROR[0;39m [35m8258[0;39m [2m---[0;39m [2m[isson-timer-4-1][0;39m [36mo.r.c.handler.PingConnectionHandler     [0;39m [2m:[0;39m Unable to send PING command over channel: [id: 0x125b1c84, L:/************:56764 - R:*************/*************:6380]

org.redisson.client.RedisTimeoutException: Command execution timeout for command: (PING), params: [], Redis client: [addr=redis://*************:6380]
	at org.redisson.client.RedisConnection.lambda$async$2(RedisConnection.java:295)
	at io.netty.util.HashedWheelTimer$HashedWheelTimeout.run(HashedWheelTimer.java:713)
	at io.netty.util.concurrent.ImmediateExecutor.execute(ImmediateExecutor.java:34)
	at io.netty.util.HashedWheelTimer$HashedWheelTimeout.expire(HashedWheelTimer.java:701)
	at io.netty.util.HashedWheelTimer$HashedWheelBucket.expireTimeouts(HashedWheelTimer.java:787)
	at io.netty.util.HashedWheelTimer$Worker.run(HashedWheelTimer.java:501)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.base/java.lang.Thread.run(Thread.java:840)

[2m2025-08-02 10:02:25.049[0;39m [31mERROR[0;39m [35m8258[0;39m [2m---[0;39m [2m[isson-timer-4-1][0;39m [36mo.r.c.handler.PingConnectionHandler     [0;39m [2m:[0;39m Unable to send PING command over channel: [id: 0xe352c723, L:/************:56769 - R:*************/*************:6380]

org.redisson.client.RedisTimeoutException: Command execution timeout for command: (PING), params: [], Redis client: [addr=redis://*************:6380]
	at org.redisson.client.RedisConnection.lambda$async$2(RedisConnection.java:295)
	at io.netty.util.HashedWheelTimer$HashedWheelTimeout.run(HashedWheelTimer.java:713)
	at io.netty.util.concurrent.ImmediateExecutor.execute(ImmediateExecutor.java:34)
	at io.netty.util.HashedWheelTimer$HashedWheelTimeout.expire(HashedWheelTimer.java:701)
	at io.netty.util.HashedWheelTimer$HashedWheelBucket.expireTimeouts(HashedWheelTimer.java:787)
	at io.netty.util.HashedWheelTimer$Worker.run(HashedWheelTimer.java:501)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.base/java.lang.Thread.run(Thread.java:840)

[2m2025-08-02 10:02:25.246[0;39m [31mERROR[0;39m [35m8258[0;39m [2m---[0;39m [2m[isson-timer-4-1][0;39m [36mo.r.c.handler.PingConnectionHandler     [0;39m [2m:[0;39m Unable to send PING command over channel: [id: 0x96a8dc68, L:/************:56771 - R:*************/*************:6380]

org.redisson.client.RedisTimeoutException: Command execution timeout for command: (PING), params: [], Redis client: [addr=redis://*************:6380]
	at org.redisson.client.RedisConnection.lambda$async$2(RedisConnection.java:295)
	at io.netty.util.HashedWheelTimer$HashedWheelTimeout.run(HashedWheelTimer.java:713)
	at io.netty.util.concurrent.ImmediateExecutor.execute(ImmediateExecutor.java:34)
	at io.netty.util.HashedWheelTimer$HashedWheelTimeout.expire(HashedWheelTimer.java:701)
	at io.netty.util.HashedWheelTimer$HashedWheelBucket.expireTimeouts(HashedWheelTimer.java:787)
	at io.netty.util.HashedWheelTimer$Worker.run(HashedWheelTimer.java:501)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.base/java.lang.Thread.run(Thread.java:840)

[2m2025-08-02 10:02:25.351[0;39m [31mERROR[0;39m [35m8258[0;39m [2m---[0;39m [2m[isson-timer-4-1][0;39m [36mo.r.c.handler.PingConnectionHandler     [0;39m [2m:[0;39m Unable to send PING command over channel: [id: 0xa1945fd4, L:/************:56773 - R:*************/*************:6380]

org.redisson.client.RedisTimeoutException: Command execution timeout for command: (PING), params: [], Redis client: [addr=redis://*************:6380]
	at org.redisson.client.RedisConnection.lambda$async$2(RedisConnection.java:295)
	at io.netty.util.HashedWheelTimer$HashedWheelTimeout.run(HashedWheelTimer.java:713)
	at io.netty.util.concurrent.ImmediateExecutor.execute(ImmediateExecutor.java:34)
	at io.netty.util.HashedWheelTimer$HashedWheelTimeout.expire(HashedWheelTimer.java:701)
	at io.netty.util.HashedWheelTimer$HashedWheelBucket.expireTimeouts(HashedWheelTimer.java:787)
	at io.netty.util.HashedWheelTimer$Worker.run(HashedWheelTimer.java:501)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.base/java.lang.Thread.run(Thread.java:840)

[2m2025-08-02 10:02:25.555[0;39m [31mERROR[0;39m [35m8258[0;39m [2m---[0;39m [2m[isson-timer-4-1][0;39m [36mo.r.c.handler.PingConnectionHandler     [0;39m [2m:[0;39m Unable to send PING command over channel: [id: 0x21cf25fa, L:/************:56776 - R:*************/*************:6380]

org.redisson.client.RedisTimeoutException: Command execution timeout for command: (PING), params: [], Redis client: [addr=redis://*************:6380]
	at org.redisson.client.RedisConnection.lambda$async$2(RedisConnection.java:295)
	at io.netty.util.HashedWheelTimer$HashedWheelTimeout.run(HashedWheelTimer.java:713)
	at io.netty.util.concurrent.ImmediateExecutor.execute(ImmediateExecutor.java:34)
	at io.netty.util.HashedWheelTimer$HashedWheelTimeout.expire(HashedWheelTimer.java:701)
	at io.netty.util.HashedWheelTimer$HashedWheelBucket.expireTimeouts(HashedWheelTimer.java:787)
	at io.netty.util.HashedWheelTimer$Worker.run(HashedWheelTimer.java:501)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.base/java.lang.Thread.run(Thread.java:840)

[2m2025-08-02 10:02:25.653[0;39m [31mERROR[0;39m [35m8258[0;39m [2m---[0;39m [2m[isson-timer-4-1][0;39m [36mo.r.c.handler.PingConnectionHandler     [0;39m [2m:[0;39m Unable to send PING command over channel: [id: 0xc19fd542, L:/************:56779 - R:*************/*************:6380]

org.redisson.client.RedisTimeoutException: Command execution timeout for command: (PING), params: [], Redis client: [addr=redis://*************:6380]
	at org.redisson.client.RedisConnection.lambda$async$2(RedisConnection.java:295)
	at io.netty.util.HashedWheelTimer$HashedWheelTimeout.run(HashedWheelTimer.java:713)
	at io.netty.util.concurrent.ImmediateExecutor.execute(ImmediateExecutor.java:34)
	at io.netty.util.HashedWheelTimer$HashedWheelTimeout.expire(HashedWheelTimer.java:701)
	at io.netty.util.HashedWheelTimer$HashedWheelBucket.expireTimeouts(HashedWheelTimer.java:787)
	at io.netty.util.HashedWheelTimer$Worker.run(HashedWheelTimer.java:501)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.base/java.lang.Thread.run(Thread.java:840)

[2m2025-08-02 10:02:25.755[0;39m [31mERROR[0;39m [35m8258[0;39m [2m---[0;39m [2m[isson-timer-4-1][0;39m [36mo.r.c.handler.PingConnectionHandler     [0;39m [2m:[0;39m Unable to send PING command over channel: [id: 0xc0c411f3, L:/************:56780 - R:*************/*************:6380]

org.redisson.client.RedisTimeoutException: Command execution timeout for command: (PING), params: [], Redis client: [addr=redis://*************:6380]
	at org.redisson.client.RedisConnection.lambda$async$2(RedisConnection.java:295)
	at io.netty.util.HashedWheelTimer$HashedWheelTimeout.run(HashedWheelTimer.java:713)
	at io.netty.util.concurrent.ImmediateExecutor.execute(ImmediateExecutor.java:34)
	at io.netty.util.HashedWheelTimer$HashedWheelTimeout.expire(HashedWheelTimer.java:701)
	at io.netty.util.HashedWheelTimer$HashedWheelBucket.expireTimeouts(HashedWheelTimer.java:787)
	at io.netty.util.HashedWheelTimer$Worker.run(HashedWheelTimer.java:501)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.base/java.lang.Thread.run(Thread.java:840)

[2m2025-08-02 10:02:25.952[0;39m [31mERROR[0;39m [35m8258[0;39m [2m---[0;39m [2m[isson-timer-4-1][0;39m [36mo.r.c.handler.PingConnectionHandler     [0;39m [2m:[0;39m Unable to send PING command over channel: [id: 0x9e44301c, L:/************:56782 - R:*************/*************:6380]

org.redisson.client.RedisTimeoutException: Command execution timeout for command: (PING), params: [], Redis client: [addr=redis://*************:6380]
	at org.redisson.client.RedisConnection.lambda$async$2(RedisConnection.java:295)
	at io.netty.util.HashedWheelTimer$HashedWheelTimeout.run(HashedWheelTimer.java:713)
	at io.netty.util.concurrent.ImmediateExecutor.execute(ImmediateExecutor.java:34)
	at io.netty.util.HashedWheelTimer$HashedWheelTimeout.expire(HashedWheelTimer.java:701)
	at io.netty.util.HashedWheelTimer$HashedWheelBucket.expireTimeouts(HashedWheelTimer.java:787)
	at io.netty.util.HashedWheelTimer$Worker.run(HashedWheelTimer.java:501)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.base/java.lang.Thread.run(Thread.java:840)

[2m2025-08-02 10:02:26.055[0;39m [31mERROR[0;39m [35m8258[0;39m [2m---[0;39m [2m[isson-timer-4-1][0;39m [36mo.r.c.handler.PingConnectionHandler     [0;39m [2m:[0;39m Unable to send PING command over channel: [id: 0x7ab96286, L:/************:56784 - R:*************/*************:6380]

org.redisson.client.RedisTimeoutException: Command execution timeout for command: (PING), params: [], Redis client: [addr=redis://*************:6380]
	at org.redisson.client.RedisConnection.lambda$async$2(RedisConnection.java:295)
	at io.netty.util.HashedWheelTimer$HashedWheelTimeout.run(HashedWheelTimer.java:713)
	at io.netty.util.concurrent.ImmediateExecutor.execute(ImmediateExecutor.java:34)
	at io.netty.util.HashedWheelTimer$HashedWheelTimeout.expire(HashedWheelTimer.java:701)
	at io.netty.util.HashedWheelTimer$HashedWheelBucket.expireTimeouts(HashedWheelTimer.java:787)
	at io.netty.util.HashedWheelTimer$Worker.run(HashedWheelTimer.java:501)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.base/java.lang.Thread.run(Thread.java:840)

[2m2025-08-02 10:02:26.155[0;39m [31mERROR[0;39m [35m8258[0;39m [2m---[0;39m [2m[isson-timer-4-1][0;39m [36mo.r.c.handler.PingConnectionHandler     [0;39m [2m:[0;39m Unable to send PING command over channel: [id: 0x147f6974, L:/************:56786 - R:*************/*************:6380]

org.redisson.client.RedisTimeoutException: Command execution timeout for command: (PING), params: [], Redis client: [addr=redis://*************:6380]
	at org.redisson.client.RedisConnection.lambda$async$2(RedisConnection.java:295)
	at io.netty.util.HashedWheelTimer$HashedWheelTimeout.run(HashedWheelTimer.java:713)
	at io.netty.util.concurrent.ImmediateExecutor.execute(ImmediateExecutor.java:34)
	at io.netty.util.HashedWheelTimer$HashedWheelTimeout.expire(HashedWheelTimer.java:701)
	at io.netty.util.HashedWheelTimer$HashedWheelBucket.expireTimeouts(HashedWheelTimer.java:787)
	at io.netty.util.HashedWheelTimer$Worker.run(HashedWheelTimer.java:501)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.base/java.lang.Thread.run(Thread.java:840)

[2m2025-08-02 10:02:26.251[0;39m [31mERROR[0;39m [35m8258[0;39m [2m---[0;39m [2m[isson-timer-4-1][0;39m [36mo.r.c.handler.PingConnectionHandler     [0;39m [2m:[0;39m Unable to send PING command over channel: [id: 0x4d5be931, L:/************:56788 - R:*************/*************:6380]

org.redisson.client.RedisTimeoutException: Command execution timeout for command: (PING), params: [], Redis client: [addr=redis://*************:6380]
	at org.redisson.client.RedisConnection.lambda$async$2(RedisConnection.java:295)
	at io.netty.util.HashedWheelTimer$HashedWheelTimeout.run(HashedWheelTimer.java:713)
	at io.netty.util.concurrent.ImmediateExecutor.execute(ImmediateExecutor.java:34)
	at io.netty.util.HashedWheelTimer$HashedWheelTimeout.expire(HashedWheelTimer.java:701)
	at io.netty.util.HashedWheelTimer$HashedWheelBucket.expireTimeouts(HashedWheelTimer.java:787)
	at io.netty.util.HashedWheelTimer$Worker.run(HashedWheelTimer.java:501)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.base/java.lang.Thread.run(Thread.java:840)

[2m2025-08-02 10:02:26.451[0;39m [31mERROR[0;39m [35m8258[0;39m [2m---[0;39m [2m[isson-timer-4-1][0;39m [36mo.r.c.handler.PingConnectionHandler     [0;39m [2m:[0;39m Unable to send PING command over channel: [id: 0x38991a60, L:/************:56792 - R:*************/*************:6380]

org.redisson.client.RedisTimeoutException: Command execution timeout for command: (PING), params: [], Redis client: [addr=redis://*************:6380]
	at org.redisson.client.RedisConnection.lambda$async$2(RedisConnection.java:295)
	at io.netty.util.HashedWheelTimer$HashedWheelTimeout.run(HashedWheelTimer.java:713)
	at io.netty.util.concurrent.ImmediateExecutor.execute(ImmediateExecutor.java:34)
	at io.netty.util.HashedWheelTimer$HashedWheelTimeout.expire(HashedWheelTimer.java:701)
	at io.netty.util.HashedWheelTimer$HashedWheelBucket.expireTimeouts(HashedWheelTimer.java:787)
	at io.netty.util.HashedWheelTimer$Worker.run(HashedWheelTimer.java:501)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.base/java.lang.Thread.run(Thread.java:840)

[2m2025-08-02 10:02:26.555[0;39m [31mERROR[0;39m [35m8258[0;39m [2m---[0;39m [2m[isson-timer-4-1][0;39m [36mo.r.c.handler.PingConnectionHandler     [0;39m [2m:[0;39m Unable to send PING command over channel: [id: 0x0fdd01c2, L:/************:56794 - R:*************/*************:6380]

org.redisson.client.RedisTimeoutException: Command execution timeout for command: (PING), params: [], Redis client: [addr=redis://*************:6380]
	at org.redisson.client.RedisConnection.lambda$async$2(RedisConnection.java:295)
	at io.netty.util.HashedWheelTimer$HashedWheelTimeout.run(HashedWheelTimer.java:713)
	at io.netty.util.concurrent.ImmediateExecutor.execute(ImmediateExecutor.java:34)
	at io.netty.util.HashedWheelTimer$HashedWheelTimeout.expire(HashedWheelTimer.java:701)
	at io.netty.util.HashedWheelTimer$HashedWheelBucket.expireTimeouts(HashedWheelTimer.java:787)
	at io.netty.util.HashedWheelTimer$Worker.run(HashedWheelTimer.java:501)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.base/java.lang.Thread.run(Thread.java:840)

[2m2025-08-02 10:02:26.650[0;39m [31mERROR[0;39m [35m8258[0;39m [2m---[0;39m [2m[isson-timer-4-1][0;39m [36mo.r.c.handler.PingConnectionHandler     [0;39m [2m:[0;39m Unable to send PING command over channel: [id: 0xc053b258, L:/************:56796 - R:*************/*************:6380]

org.redisson.client.RedisTimeoutException: Command execution timeout for command: (PING), params: [], Redis client: [addr=redis://*************:6380]
	at org.redisson.client.RedisConnection.lambda$async$2(RedisConnection.java:295)
	at io.netty.util.HashedWheelTimer$HashedWheelTimeout.run(HashedWheelTimer.java:713)
	at io.netty.util.concurrent.ImmediateExecutor.execute(ImmediateExecutor.java:34)
	at io.netty.util.HashedWheelTimer$HashedWheelTimeout.expire(HashedWheelTimer.java:701)
	at io.netty.util.HashedWheelTimer$HashedWheelBucket.expireTimeouts(HashedWheelTimer.java:787)
	at io.netty.util.HashedWheelTimer$Worker.run(HashedWheelTimer.java:501)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.base/java.lang.Thread.run(Thread.java:840)

[2m2025-08-02 10:02:26.748[0;39m [31mERROR[0;39m [35m8258[0;39m [2m---[0;39m [2m[isson-timer-4-1][0;39m [36mo.r.c.handler.PingConnectionHandler     [0;39m [2m:[0;39m Unable to send PING command over channel: [id: 0xff30a9eb, L:/************:56798 - R:*************/*************:6380]

org.redisson.client.RedisTimeoutException: Command execution timeout for command: (PING), params: [], Redis client: [addr=redis://*************:6380]
	at org.redisson.client.RedisConnection.lambda$async$2(RedisConnection.java:295)
	at io.netty.util.HashedWheelTimer$HashedWheelTimeout.run(HashedWheelTimer.java:713)
	at io.netty.util.concurrent.ImmediateExecutor.execute(ImmediateExecutor.java:34)
	at io.netty.util.HashedWheelTimer$HashedWheelTimeout.expire(HashedWheelTimer.java:701)
	at io.netty.util.HashedWheelTimer$HashedWheelBucket.expireTimeouts(HashedWheelTimer.java:787)
	at io.netty.util.HashedWheelTimer$Worker.run(HashedWheelTimer.java:501)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.base/java.lang.Thread.run(Thread.java:840)

[2m2025-08-02 10:05:55.096[0;39m [32m INFO[0;39m [35m8258[0;39m [2m---[0;39m [2m[168.9.254:37017][0;39m [36morg.mongodb.driver.cluster              [0;39m [2m:[0;39m Exception in monitor thread while connecting to server *************:37017

com.mongodb.MongoSocketReadTimeoutException: Timeout while receiving message
	at com.mongodb.internal.connection.InternalStreamConnection.createReadTimeoutException(InternalStreamConnection.java:819)
	at com.mongodb.internal.connection.InternalStreamConnection.translateReadException(InternalStreamConnection.java:807)
	at com.mongodb.internal.connection.InternalStreamConnection.receiveResponseBuffers(InternalStreamConnection.java:857)
	at com.mongodb.internal.connection.InternalStreamConnection.receiveCommandMessageResponse(InternalStreamConnection.java:517)
	at com.mongodb.internal.connection.InternalStreamConnection.receive(InternalStreamConnection.java:469)
	at com.mongodb.internal.connection.DefaultServerMonitor$ServerMonitor.lookupServerDescription(DefaultServerMonitor.java:249)
	at com.mongodb.internal.connection.DefaultServerMonitor$ServerMonitor.run(DefaultServerMonitor.java:176)
Caused by: java.net.SocketTimeoutException: Read timed out
	at java.base/sun.nio.ch.NioSocketImpl.timedRead(NioSocketImpl.java:288)
	at java.base/sun.nio.ch.NioSocketImpl.implRead(NioSocketImpl.java:314)
	at java.base/sun.nio.ch.NioSocketImpl.read(NioSocketImpl.java:355)
	at java.base/sun.nio.ch.NioSocketImpl$1.read(NioSocketImpl.java:808)
	at java.base/java.net.Socket$SocketInputStream.read(Socket.java:976)
	at com.mongodb.internal.connection.SocketStream.read(SocketStream.java:182)
	at com.mongodb.internal.connection.InternalStreamConnection.receiveResponseBuffers(InternalStreamConnection.java:824)
	... 4 common frames omitted

[2m2025-08-02 10:05:55.367[0;39m [32m INFO[0;39m [35m8258[0;39m [2m---[0;39m [2m[168.9.254:37017][0;39m [36morg.mongodb.driver.cluster              [0;39m [2m:[0;39m Monitor thread successfully connected to server with description ServerDescription{address=*************:37017, type=STANDALONE, cryptd=false, state=CONNECTED, ok=true, minWireVersion=0, maxWireVersion=13, maxDocumentSize=16777216, logicalSessionTimeoutMinutes=30, roundTripTimeNanos=89573708, minRoundTripTimeNanos=0}
[2m2025-08-02 10:06:13.031[0;39m [31mERROR[0;39m [35m8258[0;39m [2m---[0;39m [2m[isson-timer-4-1][0;39m [36mo.r.c.handler.PingConnectionHandler     [0;39m [2m:[0;39m Unable to send PING command over channel: [id: 0xa4dc43db, L:/************:60837 - R:*************/*************:6380]

org.redisson.client.RedisTimeoutException: Command execution timeout for command: (PING), params: [], Redis client: [addr=redis://*************:6380]
	at org.redisson.client.RedisConnection.lambda$async$2(RedisConnection.java:295)
	at io.netty.util.HashedWheelTimer$HashedWheelTimeout.run(HashedWheelTimer.java:713)
	at io.netty.util.concurrent.ImmediateExecutor.execute(ImmediateExecutor.java:34)
	at io.netty.util.HashedWheelTimer$HashedWheelTimeout.expire(HashedWheelTimer.java:701)
	at io.netty.util.HashedWheelTimer$HashedWheelBucket.expireTimeouts(HashedWheelTimer.java:787)
	at io.netty.util.HashedWheelTimer$Worker.run(HashedWheelTimer.java:501)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.base/java.lang.Thread.run(Thread.java:840)

[2m2025-08-02 10:58:46.759[0;39m [32m INFO[0;39m [35m22921[0;39m [2m---[0;39m [2m[kground-preinit][0;39m [36mo.h.validator.internal.util.Version     [0;39m [2m:[0;39m HV000001: Hibernate Validator 8.0.2.Final
[2m2025-08-02 10:58:47.081[0;39m [32m INFO[0;39m [35m22921[0;39m [2m---[0;39m [2m[           main][0;39m [36mcom.sanyth.App                          [0;39m [2m:[0;39m Starting App using Java 17.0.15 with PID 22921 (/Users/<USER>/WorkSpace/sanyth-dev/sanyth-dev-platform/sanyth-server/target/classes started by paynexc in /Users/<USER>/WorkSpace/sanyth-dev)
[2m2025-08-02 10:58:47.082[0;39m [32mDEBUG[0;39m [35m22921[0;39m [2m---[0;39m [2m[           main][0;39m [36mcom.sanyth.App                          [0;39m [2m:[0;39m Running with Spring Boot v3.5.3, Spring v6.2.8
[2m2025-08-02 10:58:47.082[0;39m [32m INFO[0;39m [35m22921[0;39m [2m---[0;39m [2m[           main][0;39m [36mcom.sanyth.App                          [0;39m [2m:[0;39m The following 1 profile is active: "dev"
[2m2025-08-02 10:58:47.206[0;39m [32m INFO[0;39m [35m22921[0;39m [2m---[0;39m [2m[           main][0;39m [36mo.s.b.devtools.restart.ChangeableUrls   [0;39m [2m:[0;39m The Class-Path manifest attribute in /Users/<USER>/.m2/repository/javax/mail/mail/1.4/mail-1.4.jar referenced one or more files that do not exist: file:/Users/<USER>/.m2/repository/javax/mail/mail/1.4/activation.jar
[2m2025-08-02 10:58:47.206[0;39m [32m INFO[0;39m [35m22921[0;39m [2m---[0;39m [2m[           main][0;39m [36mo.s.b.devtools.restart.ChangeableUrls   [0;39m [2m:[0;39m The Class-Path manifest attribute in /Users/<USER>/.m2/repository/com/oracle/ojdbc8/23.2.0.0.0/ojdbc8-23.2.0.0.0.jar referenced one or more files that do not exist: file:/Users/<USER>/.m2/repository/com/oracle/ojdbc8/23.2.0.0.0/oraclepki.jar
[2m2025-08-02 10:58:47.206[0;39m [32m INFO[0;39m [35m22921[0;39m [2m---[0;39m [2m[           main][0;39m [36m.e.DevToolsPropertyDefaultsPostProcessor[0;39m [2m:[0;39m Devtools property defaults active! Set 'spring.devtools.add-properties' to 'false' to disable
[2m2025-08-02 10:58:47.206[0;39m [32m INFO[0;39m [35m22921[0;39m [2m---[0;39m [2m[           main][0;39m [36m.e.DevToolsPropertyDefaultsPostProcessor[0;39m [2m:[0;39m For additional web related logging consider setting the 'logging.level.web' property to 'DEBUG'
[2m2025-08-02 10:58:48.375[0;39m [33m WARN[0;39m [35m22921[0;39m [2m---[0;39m [2m[           main][0;39m [36mConfigServletWebServerApplicationContext[0;39m [2m:[0;39m Exception encountered during context initialization - cancelling refresh attempt: java.lang.IllegalArgumentException: Could not find class [com.sanyth.langchain.core.config.ChatProps]
[2m2025-08-02 10:58:48.386[0;39m [32m INFO[0;39m [35m22921[0;39m [2m---[0;39m [2m[           main][0;39m [36m.s.b.a.l.ConditionEvaluationReportLogger[0;39m [2m:[0;39m 

Error starting ApplicationContext. To display the condition evaluation report re-run your application with 'debug' enabled.
[2m2025-08-02 10:58:48.415[0;39m [31mERROR[0;39m [35m22921[0;39m [2m---[0;39m [2m[           main][0;39m [36mo.s.boot.SpringApplication              [0;39m [2m:[0;39m Application run failed

java.lang.IllegalArgumentException: Could not find class [com.sanyth.langchain.core.config.ChatProps]
	at org.springframework.util.ClassUtils.resolveClassName(ClassUtils.java:372)
	at org.springframework.core.annotation.TypeMappedAnnotation.adapt(TypeMappedAnnotation.java:466)
	at org.springframework.core.annotation.TypeMappedAnnotation.getValue(TypeMappedAnnotation.java:391)
	at org.springframework.core.annotation.TypeMappedAnnotation.asMap(TypeMappedAnnotation.java:278)
	at org.springframework.core.annotation.AbstractMergedAnnotation.asAnnotationAttributes(AbstractMergedAnnotation.java:191)
	at org.springframework.core.type.AnnotatedTypeMetadata.getAnnotationAttributes(AnnotatedTypeMetadata.java:118)
	at org.springframework.core.type.AnnotatedTypeMetadata.getAnnotationAttributes(AnnotatedTypeMetadata.java:91)
	at org.springframework.context.annotation.AnnotationConfigUtils.attributesFor(AnnotationConfigUtils.java:285)
	at org.springframework.context.annotation.AutoProxyRegistrar.registerBeanDefinitions(AutoProxyRegistrar.java:63)
	at org.springframework.context.annotation.ImportBeanDefinitionRegistrar.registerBeanDefinitions(ImportBeanDefinitionRegistrar.java:86)
	at org.springframework.context.annotation.ConfigurationClassBeanDefinitionReader.lambda$loadBeanDefinitionsFromRegistrars$1(ConfigurationClassBeanDefinitionReader.java:398)
	at java.base/java.util.LinkedHashMap.forEach(LinkedHashMap.java:721)
	at org.springframework.context.annotation.ConfigurationClassBeanDefinitionReader.loadBeanDefinitionsFromRegistrars(ConfigurationClassBeanDefinitionReader.java:397)
	at org.springframework.context.annotation.ConfigurationClassBeanDefinitionReader.loadBeanDefinitionsForConfigurationClass(ConfigurationClassBeanDefinitionReader.java:151)
	at org.springframework.context.annotation.ConfigurationClassBeanDefinitionReader.loadBeanDefinitions(ConfigurationClassBeanDefinitionReader.java:123)
	at org.springframework.context.annotation.ConfigurationClassPostProcessor.processConfigBeanDefinitions(ConfigurationClassPostProcessor.java:430)
	at org.springframework.context.annotation.ConfigurationClassPostProcessor.postProcessBeanDefinitionRegistry(ConfigurationClassPostProcessor.java:290)
	at org.springframework.context.support.PostProcessorRegistrationDelegate.invokeBeanDefinitionRegistryPostProcessors(PostProcessorRegistrationDelegate.java:349)
	at org.springframework.context.support.PostProcessorRegistrationDelegate.invokeBeanFactoryPostProcessors(PostProcessorRegistrationDelegate.java:118)
	at org.springframework.context.support.AbstractApplicationContext.invokeBeanFactoryPostProcessors(AbstractApplicationContext.java:791)
	at org.springframework.context.support.AbstractApplicationContext.__refresh(AbstractApplicationContext.java:609)
	at org.springframework.context.support.AbstractApplicationContext.jrLockAndRefresh(AbstractApplicationContext.java:41002)
	at org.springframework.context.support.AbstractApplicationContext.refresh(AbstractApplicationContext.java:42008)
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.refresh(ServletWebServerApplicationContext.java:146)
	at org.springframework.boot.SpringApplication.refresh(SpringApplication.java:752)
	at org.springframework.boot.SpringApplication.refreshContext(SpringApplication.java:439)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:318)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1361)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1350)
	at com.sanyth.App.main(App.java:20)
Caused by: java.lang.ClassNotFoundException: com.sanyth.langchain.core.config.ChatProps
	at java.base/jdk.internal.loader.BuiltinClassLoader.loadClass(BuiltinClassLoader.java:641)
	at java.base/jdk.internal.loader.ClassLoaders$AppClassLoader.loadClass(ClassLoaders.java:188)
	at java.base/java.lang.ClassLoader.loadClass(ClassLoader.java:525)
	at java.base/java.lang.Class.forName0(Native Method)
	at java.base/java.lang.Class.forName(Class.java:467)
	at org.springframework.util.ClassUtils.forName(ClassUtils.java:321)
	at org.springframework.util.ClassUtils.resolveClassName(ClassUtils.java:362)
	... 29 common frames omitted

