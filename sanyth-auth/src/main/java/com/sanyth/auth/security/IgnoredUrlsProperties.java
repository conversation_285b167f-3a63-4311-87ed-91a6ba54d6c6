package com.sanyth.auth.security;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;
import org.springframework.util.AntPathMatcher;

import java.util.List;

/**
 * @since 2025/6/30.
 */
@Data
@Configuration
@ConfigurationProperties(prefix = "security")
public class IgnoredUrlsProperties {
    private static final AntPathMatcher antPathMatcher = new AntPathMatcher();
    private List<String> ignoreAuthPaths;

    public boolean ignore(String requestUrl) {
        if (ignoreAuthPaths == null || ignoreAuthPaths.isEmpty() || requestUrl == null) {
            return false;
        }

        for (String pattern : ignoreAuthPaths) {
            if (pattern != null && antPathMatcher.match(pattern, requestUrl)) {
                return true;
            }
        }
        return false;
    }
}
