package com.sanyth.auth.security;

import com.sanyth.core.config.ConfigProperties;
import com.sanyth.upms.system.service.LoginRecordService;
import com.sanyth.upms.system.service.SysAccountService;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Profile;
import org.springframework.core.annotation.Order;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.security.config.annotation.method.configuration.EnableMethodSecurity;
import org.springframework.security.config.annotation.web.builders.HttpSecurity;
import org.springframework.security.config.annotation.web.configuration.EnableWebSecurity;
import org.springframework.security.config.annotation.web.configurers.AbstractHttpConfigurer;
import org.springframework.security.config.http.SessionCreationPolicy;
import org.springframework.security.crypto.bcrypt.BCryptPasswordEncoder;
import org.springframework.security.web.SecurityFilterChain;
import org.springframework.security.web.authentication.UsernamePasswordAuthenticationFilter;
import org.springframework.security.web.authentication.www.BasicAuthenticationFilter;
import org.springframework.security.web.context.DelegatingSecurityContextRepository;
import org.springframework.security.web.context.HttpSessionSecurityContextRepository;
import org.springframework.security.web.context.RequestAttributeSecurityContextRepository;

/**
 * Created by JIANGPING on 2024/8/16.
 */
@Configuration
//@EnableMethodSecurity
@EnableWebSecurity
public class SecurityConfig {
//    @Resource
//    private JwtAccessDeniedHandler accessDeniedHandler;
//    @Resource
//    private JwtAuthenticationEntryPoint authenticationEntryPoint;

    @Configuration
    @Profile("dev")
    public static class DevMethodSecurityConfig {

    }

    @EnableMethodSecurity
    @Configuration
    @Profile("!dev")
    public static class PrdMethodSecurityConfig {

    }

    @Bean
    public JwtAuthenticationFilter jwtAuthenticationFilter(ConfigProperties configProperties, LoginRecordService loginRecordService, SysAccountService sysAccountService, RedisTemplate<String, Object> redisTemplate, IgnoredUrlsProperties ignoredUrlsProperties) {
        return new JwtAuthenticationFilter(configProperties, loginRecordService, sysAccountService, redisTemplate, ignoredUrlsProperties);
    }

    @Order(99)
    @Bean
    public SecurityFilterChain unifiedAuthSecurityFilterChain(HttpSecurity http) throws Exception {
        return http
                .securityMatcher("/unified-auth/open/**")
                .authorizeHttpRequests(auth ->
                        auth.anyRequest().permitAll())
                .addFilterBefore(new UnifiedAuthCustomFilter(), BasicAuthenticationFilter.class)
                .sessionManagement(sessionManagement -> sessionManagement.sessionCreationPolicy(SessionCreationPolicy.ALWAYS))
                .csrf(AbstractHttpConfigurer::disable)
                .cors(AbstractHttpConfigurer::disable)
                .build();
    }

//    @Order(100)
    @Bean
    public SecurityFilterChain securityFilterChain(HttpSecurity http, JwtAuthenticationFilter jwtAuthenticationFilter, IgnoredUrlsProperties ignoredUrlsProperties) throws Exception {
        return http
                .csrf(AbstractHttpConfigurer::disable)
                .cors(AbstractHttpConfigurer::disable)
                .authorizeHttpRequests(auth -> {
                    // 使用配置的忽略路径
                    if (ignoredUrlsProperties != null && ignoredUrlsProperties.getIgnoreAuthPaths() != null) {
                        String[] ignoredPaths = ignoredUrlsProperties.getIgnoreAuthPaths().toArray(new String[0]);
                        auth.requestMatchers(ignoredPaths).permitAll();
                    } else {
                        // 兜底：如果配置为空，使用默认的白名单路径
                        auth.requestMatchers("/api/sec_js", "/api/captcha", "/api/login", "/api/file/**",
                                "/api/cus-auth/**", "/api/msg/**", "/api/aigc/chat/**", "/v1/**").permitAll();
                    }
                    auth.anyRequest().authenticated();
                })
                .sessionManagement(s -> s.sessionCreationPolicy(SessionCreationPolicy.STATELESS))
                .exceptionHandling(e -> e
                        .authenticationEntryPoint(new JwtAuthenticationEntryPoint())
                        .accessDeniedHandler(new JwtAccessDeniedHandler()))
                .addFilterBefore(jwtAuthenticationFilter, UsernamePasswordAuthenticationFilter.class)
                .securityContext((securityContext) -> securityContext
                        .requireExplicitSave(false)
                        .securityContextRepository(new DelegatingSecurityContextRepository(
                                new RequestAttributeSecurityContextRepository(),
                                new HttpSessionSecurityContextRepository()
                        )))
                .build();
    }

    @Bean
    public BCryptPasswordEncoder bCryptPasswordEncoder() {
        return new BCryptPasswordEncoder();
    }
}
