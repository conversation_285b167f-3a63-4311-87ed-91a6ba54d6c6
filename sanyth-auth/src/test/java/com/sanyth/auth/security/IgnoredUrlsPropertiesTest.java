package com.sanyth.auth.security;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;

import java.util.Arrays;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;

/**
 * IgnoredUrlsProperties 测试类
 * 
 * @since 2025-08-02
 */
class IgnoredUrlsPropertiesTest {

    private IgnoredUrlsProperties ignoredUrlsProperties;

    @BeforeEach
    void setUp() {
        ignoredUrlsProperties = new IgnoredUrlsProperties();
        
        // 模拟配置的忽略路径
        List<String> ignoreAuthPaths = Arrays.asList(
            "/api/sec_js",
            "/api/login",
            "/api/captcha",
            "/api/cus-auth/**",
            "/api/system-info",
            "/api/cas/**",
            "/api/file/**",
            "/api/msg/**",
            "/api/aigc/chat/**",
            "/v1/**",
            "/nonlogin/**",
            "/lib/**",
            "/unified-auth/open/**"
        );
        
        ignoredUrlsProperties.setIgnoreAuthPaths(ignoreAuthPaths);
    }

    @Test
    void testIgnoreExactMatch() {
        // 测试精确匹配
        assertTrue(ignoredUrlsProperties.ignore("/api/sec_js"));
        assertTrue(ignoredUrlsProperties.ignore("/api/login"));
        assertTrue(ignoredUrlsProperties.ignore("/api/captcha"));
        assertTrue(ignoredUrlsProperties.ignore("/api/system-info"));
    }

    @Test
    void testIgnoreWildcardMatch() {
        // 测试通配符匹配
        assertTrue(ignoredUrlsProperties.ignore("/api/cus-auth/test"));
        assertTrue(ignoredUrlsProperties.ignore("/api/cus-auth/user/login"));
        assertTrue(ignoredUrlsProperties.ignore("/api/cas/login"));
        assertTrue(ignoredUrlsProperties.ignore("/api/file/upload"));
        assertTrue(ignoredUrlsProperties.ignore("/api/file/download/123"));
        assertTrue(ignoredUrlsProperties.ignore("/api/msg/send"));
        assertTrue(ignoredUrlsProperties.ignore("/api/aigc/chat/stream"));
        assertTrue(ignoredUrlsProperties.ignore("/v1/api/test"));
        assertTrue(ignoredUrlsProperties.ignore("/nonlogin/test"));
        assertTrue(ignoredUrlsProperties.ignore("/lib/js/test.js"));
        assertTrue(ignoredUrlsProperties.ignore("/unified-auth/open/test"));
    }

    @Test
    void testNotIgnored() {
        // 测试不应该被忽略的路径
        assertFalse(ignoredUrlsProperties.ignore("/api/user"));
        assertFalse(ignoredUrlsProperties.ignore("/api/admin/users"));
        assertFalse(ignoredUrlsProperties.ignore("/api/protected"));
        assertFalse(ignoredUrlsProperties.ignore("/admin/dashboard"));
        assertFalse(ignoredUrlsProperties.ignore("/secure/data"));
    }

    @Test
    void testNullAndEmptyValues() {
        // 测试空值处理
        IgnoredUrlsProperties emptyProperties = new IgnoredUrlsProperties();
        
        // 测试空列表
        emptyProperties.setIgnoreAuthPaths(Arrays.asList());
        assertFalse(emptyProperties.ignore("/api/login"));
        
        // 测试null列表
        emptyProperties.setIgnoreAuthPaths(null);
        assertFalse(emptyProperties.ignore("/api/login"));
        
        // 测试null URL
        assertFalse(ignoredUrlsProperties.ignore(null));
    }

    @Test
    void testCaseSensitive() {
        // 测试大小写敏感性
        assertTrue(ignoredUrlsProperties.ignore("/api/login"));
        assertFalse(ignoredUrlsProperties.ignore("/API/LOGIN"));
        assertFalse(ignoredUrlsProperties.ignore("/Api/Login"));
    }

    @Test
    void testComplexPaths() {
        // 测试复杂路径
        assertTrue(ignoredUrlsProperties.ignore("/api/file/inline/image.jpg"));
        assertTrue(ignoredUrlsProperties.ignore("/api/file/attachment/document.pdf"));
        assertTrue(ignoredUrlsProperties.ignore("/unified-auth/open/oauth/token"));
        assertTrue(ignoredUrlsProperties.ignore("/v1/chat/completions"));
    }
}
