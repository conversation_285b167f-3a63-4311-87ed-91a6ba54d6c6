package com.sanyth.auth.security;

import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.TestPropertySource;

import java.util.Arrays;

import static org.junit.jupiter.api.Assertions.*;

/**
 * SecurityConfig 集成测试
 * 验证 IgnoredUrlsProperties 配置是否正确注入和工作
 * 
 * @since 2025-08-02
 */
@SpringBootTest(classes = {IgnoredUrlsProperties.class})
@TestPropertySource(properties = {
    "security.ignore-auth-paths[0]=/api/sec_js",
    "security.ignore-auth-paths[1]=/api/login",
    "security.ignore-auth-paths[2]=/api/captcha",
    "security.ignore-auth-paths[3]=/api/cus-auth/**",
    "security.ignore-auth-paths[4]=/api/system-info",
    "security.ignore-auth-paths[5]=/api/cas/**",
    "security.ignore-auth-paths[6]=/api/file/**",
    "security.ignore-auth-paths[7]=/api/msg/**",
    "security.ignore-auth-paths[8]=/api/aigc/chat/**",
    "security.ignore-auth-paths[9]=/v1/**",
    "security.ignore-auth-paths[10]=/nonlogin/**",
    "security.ignore-auth-paths[11]=/lib/**",
    "security.ignore-auth-paths[12]=/unified-auth/open/**"
})
class SecurityConfigIntegrationTest {

    @Autowired
    private IgnoredUrlsProperties ignoredUrlsProperties;

    @Test
    void testIgnoredUrlsPropertiesInjection() {
        // 验证配置是否正确注入
        assertNotNull(ignoredUrlsProperties);
        assertNotNull(ignoredUrlsProperties.getIgnoreAuthPaths());
        assertFalse(ignoredUrlsProperties.getIgnoreAuthPaths().isEmpty());
        
        // 验证配置的路径数量
        assertEquals(13, ignoredUrlsProperties.getIgnoreAuthPaths().size());
        
        // 验证特定路径是否存在
        assertTrue(ignoredUrlsProperties.getIgnoreAuthPaths().contains("/api/login"));
        assertTrue(ignoredUrlsProperties.getIgnoreAuthPaths().contains("/api/cus-auth/**"));
        assertTrue(ignoredUrlsProperties.getIgnoreAuthPaths().contains("/unified-auth/open/**"));
    }

    @Test
    void testIgnoreMethodWithInjectedConfig() {
        // 测试使用注入的配置进行路径匹配
        assertTrue(ignoredUrlsProperties.ignore("/api/login"));
        assertTrue(ignoredUrlsProperties.ignore("/api/cus-auth/test"));
        assertTrue(ignoredUrlsProperties.ignore("/api/file/upload"));
        assertTrue(ignoredUrlsProperties.ignore("/unified-auth/open/oauth"));
        
        // 测试不应该被忽略的路径
        assertFalse(ignoredUrlsProperties.ignore("/api/user"));
        assertFalse(ignoredUrlsProperties.ignore("/admin/dashboard"));
    }

    @Test
    void testConfigurationProperties() {
        // 验证 @ConfigurationProperties 注解是否正确工作
        assertNotNull(ignoredUrlsProperties.getIgnoreAuthPaths());
        
        // 验证配置中的所有路径
        assertTrue(ignoredUrlsProperties.getIgnoreAuthPaths().contains("/api/sec_js"));
        assertTrue(ignoredUrlsProperties.getIgnoreAuthPaths().contains("/api/login"));
        assertTrue(ignoredUrlsProperties.getIgnoreAuthPaths().contains("/api/captcha"));
        assertTrue(ignoredUrlsProperties.getIgnoreAuthPaths().contains("/api/cus-auth/**"));
        assertTrue(ignoredUrlsProperties.getIgnoreAuthPaths().contains("/api/system-info"));
        assertTrue(ignoredUrlsProperties.getIgnoreAuthPaths().contains("/api/cas/**"));
        assertTrue(ignoredUrlsProperties.getIgnoreAuthPaths().contains("/api/file/**"));
        assertTrue(ignoredUrlsProperties.getIgnoreAuthPaths().contains("/api/msg/**"));
        assertTrue(ignoredUrlsProperties.getIgnoreAuthPaths().contains("/api/aigc/chat/**"));
        assertTrue(ignoredUrlsProperties.getIgnoreAuthPaths().contains("/v1/**"));
        assertTrue(ignoredUrlsProperties.getIgnoreAuthPaths().contains("/nonlogin/**"));
        assertTrue(ignoredUrlsProperties.getIgnoreAuthPaths().contains("/lib/**"));
        assertTrue(ignoredUrlsProperties.getIgnoreAuthPaths().contains("/unified-auth/open/**"));
    }
}
