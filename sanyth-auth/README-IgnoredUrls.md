# 忽略鉴权路径配置功能

## 功能概述

本功能允许通过 `application.yml` 配置文件来动态配置需要忽略JWT鉴权的URL路径，支持Ant路径匹配模式（包括通配符 `*` 和 `**`）。

## 核心组件

### 1. IgnoredUrlsProperties
- **位置**: `com.sanyth.auth.security.IgnoredUrlsProperties`
- **功能**: 读取配置文件中的忽略路径配置，提供路径匹配功能
- **注解**: `@ConfigurationProperties(prefix = "security")`

### 2. JwtAuthenticationFilter
- **修改**: 注入 `IgnoredUrlsProperties`，在 `shouldNotFilter` 方法中使用配置的路径进行判断
- **兜底机制**: 如果配置为空，使用默认的硬编码白名单路径

### 3. SecurityConfig
- **修改**: 注入 `IgnoredUrlsProperties`，在 `securityFilterChain` 中使用配置的路径设置 `permitAll()`
- **兜底机制**: 如果配置为空，使用默认的硬编码白名单路径

## 配置方式

在 `application.yml` 中添加以下配置：

```yaml
security:
  ignore-auth-paths:
    - /api/sec_js
    - /api/login
    - /api/captcha
    - /api/cus-auth/**
    - /api/system-info
    - /api/cas/**
    - /api/file/**
    - /api/msg/**
    - /api/aigc/chat/**
    - /v1/**
    - /nonlogin/**
    - /lib/**
    - /unified-auth/open/**
```

## 路径匹配规则

使用 Spring 的 `AntPathMatcher` 进行路径匹配，支持以下模式：

- **精确匹配**: `/api/login` 只匹配 `/api/login`
- **单级通配符**: `/api/*/test` 匹配 `/api/user/test`、`/api/admin/test` 等
- **多级通配符**: `/api/**` 匹配 `/api/` 下的所有路径，如 `/api/user/login`、`/api/admin/user/list` 等

## 示例

### 配置示例
```yaml
security:
  ignore-auth-paths:
    - /public/**          # 忽略所有 /public/ 下的路径
    - /api/auth/login      # 忽略登录接口
    - /api/auth/register   # 忽略注册接口
    - /health              # 忽略健康检查
    - /actuator/**         # 忽略所有监控端点
```

### 匹配示例
- `/public/css/style.css` ✅ 匹配 `/public/**`
- `/api/auth/login` ✅ 匹配 `/api/auth/login`
- `/api/user/profile` ❌ 不匹配任何规则，需要鉴权
- `/actuator/health` ✅ 匹配 `/actuator/**`

## 工作原理

### 1. 过滤器层面 (JwtAuthenticationFilter)
```java
@Override
protected boolean shouldNotFilter(HttpServletRequest request) throws ServletException {
    // 使用配置的忽略路径进行判断
    String requestURI = request.getRequestURI();
    if (ignoredUrlsProperties != null && ignoredUrlsProperties.getIgnoreAuthPaths() != null) {
        return ignoredUrlsProperties.ignore(requestURI);
    }
    
    // 兜底：如果配置为空，使用默认的白名单路径
    return requestURI.contains("/api/sec_js") || requestURI.contains("/api/login")
            || requestURI.contains("/api/captcha") || requestURI.contains("/api/cus-auth");
}
```

### 2. Security配置层面 (SecurityConfig)
```java
.authorizeHttpRequests(auth -> {
    // 使用配置的忽略路径
    if (ignoredUrlsProperties != null && ignoredUrlsProperties.getIgnoreAuthPaths() != null) {
        String[] ignoredPaths = ignoredUrlsProperties.getIgnoreAuthPaths().toArray(new String[0]);
        auth.requestMatchers(ignoredPaths).permitAll();
    } else {
        // 兜底：如果配置为空，使用默认的白名单路径
        auth.requestMatchers("/api/sec_js", "/api/captcha", "/api/login", "/api/file/**",
                "/api/cus-auth/**", "/api/msg/**", "/api/aigc/chat/**", "/v1/**").permitAll();
    }
    auth.anyRequest().authenticated();
})
```

## 优势

1. **配置化管理**: 无需修改代码，通过配置文件即可调整忽略路径
2. **支持通配符**: 使用Ant路径匹配，支持灵活的路径模式
3. **兜底机制**: 配置为空时自动使用默认路径，确保系统稳定性
4. **双重保护**: 在过滤器和Security配置两个层面都进行了处理
5. **易于维护**: 集中管理所有忽略路径，便于维护和审计

## 注意事项

1. **安全考虑**: 请谨慎配置忽略路径，避免将敏感接口加入忽略列表
2. **路径格式**: 路径必须以 `/` 开头
3. **大小写敏感**: 路径匹配是大小写敏感的
4. **配置生效**: 修改配置后需要重启应用才能生效
5. **测试验证**: 建议在测试环境充分验证配置的正确性

## 测试

项目包含了完整的单元测试和集成测试：

- `IgnoredUrlsPropertiesTest`: 测试路径匹配逻辑
- `SecurityConfigIntegrationTest`: 测试配置注入和集成功能

运行测试：
```bash
mvn test -Dtest=IgnoredUrlsPropertiesTest
mvn test -Dtest=SecurityConfigIntegrationTest
```
